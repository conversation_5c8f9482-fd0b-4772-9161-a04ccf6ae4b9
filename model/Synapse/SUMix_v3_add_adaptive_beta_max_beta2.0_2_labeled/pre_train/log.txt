[15:06:39.219] Namespace(base_lr=0.01, batch_size=24, consistency=0.1, consistency_rampup=200.0, deterministic=1, exp='Synapse/SUMix_v3_add_adaptive_beta_max_beta2.0', labeled_bs=12, labeled_num=2, labelnum=2, magnitude=6.0, max_iterations=30000, model='unet', num_classes=9, num_labels=20, patch_size=[256, 256], pre_iterations=10000, root_path='/home/<USER>/data/synapse', s_param=6, seed=1337, skip_pretrain=0, temperature=0.8, test_interval=1500, test_list='test_vol.txt', train_list='train.txt', u_weight=0.5)
[15:06:40.082] Start pre_training
[15:06:40.082] 21 iterations per epoch
[15:06:42.659] iteration 1: loss: 3.254364, mix_dice: 1.865278, mix_ce: 4.643451
[15:06:42.777] iteration 2: loss: 3.110372, mix_dice: 1.882224, mix_ce: 4.338519
[15:06:42.879] iteration 3: loss: 2.832451, mix_dice: 1.869954, mix_ce: 3.794948
[15:06:42.984] iteration 4: loss: 2.453106, mix_dice: 1.806569, mix_ce: 3.099643
[15:06:43.091] iteration 5: loss: 2.064363, mix_dice: 1.792818, mix_ce: 2.335909
[15:06:43.198] iteration 6: loss: 1.695371, mix_dice: 1.746379, mix_ce: 1.644362
[15:06:43.306] iteration 7: loss: 1.398680, mix_dice: 1.691141, mix_ce: 1.106220
[15:06:43.416] iteration 8: loss: 1.259944, mix_dice: 1.653957, mix_ce: 0.865931
[15:06:43.525] iteration 9: loss: 1.101166, mix_dice: 1.705883, mix_ce: 0.496448
[15:06:43.648] iteration 10: loss: 1.123038, mix_dice: 1.660274, mix_ce: 0.585802
[15:06:43.770] iteration 11: loss: 1.057276, mix_dice: 1.690319, mix_ce: 0.424232
[15:06:43.881] iteration 12: loss: 1.080134, mix_dice: 1.685118, mix_ce: 0.475150
[15:06:43.981] iteration 13: loss: 1.069770, mix_dice: 1.681743, mix_ce: 0.457796
[15:06:44.327] iteration 14: loss: 1.030713, mix_dice: 1.659824, mix_ce: 0.401602
[15:06:44.444] iteration 15: loss: 0.988874, mix_dice: 1.708694, mix_ce: 0.269055
[15:06:44.604] iteration 16: loss: 1.068250, mix_dice: 1.681841, mix_ce: 0.454659
[15:06:44.709] iteration 17: loss: 0.996130, mix_dice: 1.679182, mix_ce: 0.313078
[15:06:45.057] iteration 18: loss: 0.955283, mix_dice: 1.673114, mix_ce: 0.237453
[15:06:45.293] iteration 19: loss: 0.971608, mix_dice: 1.684643, mix_ce: 0.258573
[15:06:45.419] iteration 20: loss: 0.984397, mix_dice: 1.660601, mix_ce: 0.308193
[15:06:45.524] iteration 21: loss: 0.996128, mix_dice: 1.727981, mix_ce: 0.264275
[15:06:46.494] iteration 22: loss: 0.927266, mix_dice: 1.680400, mix_ce: 0.174133
[15:06:46.568] iteration 23: loss: 0.943241, mix_dice: 1.686484, mix_ce: 0.199998
[15:06:46.640] iteration 24: loss: 1.044954, mix_dice: 1.660766, mix_ce: 0.429142
[15:06:46.711] iteration 25: loss: 1.003378, mix_dice: 1.664899, mix_ce: 0.341857
[15:06:47.094] iteration 26: loss: 0.988663, mix_dice: 1.696704, mix_ce: 0.280622
[15:06:47.182] iteration 27: loss: 0.992632, mix_dice: 1.615974, mix_ce: 0.369290
[15:06:47.273] iteration 28: loss: 1.004973, mix_dice: 1.604936, mix_ce: 0.405009
[15:06:47.341] iteration 29: loss: 0.961895, mix_dice: 1.656968, mix_ce: 0.266823
[15:06:47.801] iteration 30: loss: 0.932582, mix_dice: 1.642482, mix_ce: 0.222682
[15:06:47.870] iteration 31: loss: 1.017413, mix_dice: 1.625859, mix_ce: 0.408968
[15:06:47.967] iteration 32: loss: 0.987125, mix_dice: 1.665113, mix_ce: 0.309138
[15:06:48.036] iteration 33: loss: 1.002881, mix_dice: 1.635740, mix_ce: 0.370021
[15:06:48.417] iteration 34: loss: 0.912839, mix_dice: 1.605861, mix_ce: 0.219817
[15:06:48.477] iteration 35: loss: 0.964008, mix_dice: 1.593064, mix_ce: 0.334952
[15:06:48.555] iteration 36: loss: 0.995341, mix_dice: 1.681505, mix_ce: 0.309177
[15:06:48.624] iteration 37: loss: 0.922163, mix_dice: 1.614442, mix_ce: 0.229884
[15:06:49.066] iteration 38: loss: 0.910296, mix_dice: 1.600565, mix_ce: 0.220026
[15:06:49.144] iteration 39: loss: 1.052284, mix_dice: 1.598628, mix_ce: 0.505941
[15:06:49.232] iteration 40: loss: 0.968320, mix_dice: 1.608500, mix_ce: 0.328140
[15:06:49.294] iteration 41: loss: 0.900284, mix_dice: 1.620742, mix_ce: 0.179826
[15:06:49.710] iteration 42: loss: 0.872903, mix_dice: 1.563828, mix_ce: 0.181978
[15:06:50.578] iteration 43: loss: 0.944730, mix_dice: 1.626192, mix_ce: 0.263267
[15:06:50.652] iteration 44: loss: 0.926853, mix_dice: 1.621483, mix_ce: 0.232222
[15:06:50.715] iteration 45: loss: 0.947156, mix_dice: 1.576332, mix_ce: 0.317980
[15:06:50.775] iteration 46: loss: 0.948903, mix_dice: 1.568901, mix_ce: 0.328905
[15:06:51.163] iteration 47: loss: 0.951367, mix_dice: 1.621584, mix_ce: 0.281150
[15:06:51.228] iteration 48: loss: 0.923764, mix_dice: 1.599586, mix_ce: 0.247942
[15:06:51.305] iteration 49: loss: 0.851567, mix_dice: 1.566888, mix_ce: 0.136246
[15:06:51.376] iteration 50: loss: 0.938456, mix_dice: 1.536251, mix_ce: 0.340661
[15:06:51.862] iteration 51: loss: 0.948924, mix_dice: 1.529630, mix_ce: 0.368218
[15:06:51.940] iteration 52: loss: 0.943181, mix_dice: 1.504081, mix_ce: 0.382281
[15:06:52.001] iteration 53: loss: 0.984717, mix_dice: 1.595819, mix_ce: 0.373616
[15:06:52.067] iteration 54: loss: 0.950695, mix_dice: 1.588637, mix_ce: 0.312754
[15:06:52.492] iteration 55: loss: 0.876542, mix_dice: 1.506819, mix_ce: 0.246265
[15:06:52.568] iteration 56: loss: 0.911896, mix_dice: 1.509847, mix_ce: 0.313945
[15:06:52.618] iteration 57: loss: 0.945291, mix_dice: 1.649246, mix_ce: 0.241335
[15:06:52.691] iteration 58: loss: 0.897154, mix_dice: 1.566916, mix_ce: 0.227392
[15:06:53.205] iteration 59: loss: 0.930725, mix_dice: 1.583878, mix_ce: 0.277572
[15:06:53.276] iteration 60: loss: 0.889959, mix_dice: 1.659271, mix_ce: 0.120648
[15:06:53.334] iteration 61: loss: 0.947501, mix_dice: 1.518822, mix_ce: 0.376180
[15:06:53.384] iteration 62: loss: 0.920006, mix_dice: 1.618345, mix_ce: 0.221668
[15:06:53.841] iteration 63: loss: 0.895067, mix_dice: 1.552796, mix_ce: 0.237338
[15:06:54.767] iteration 64: loss: 0.887504, mix_dice: 1.598636, mix_ce: 0.176372
[15:06:54.821] iteration 65: loss: 0.924124, mix_dice: 1.481687, mix_ce: 0.366561
[15:06:54.877] iteration 66: loss: 0.914190, mix_dice: 1.566065, mix_ce: 0.262316
[15:06:54.927] iteration 67: loss: 0.868474, mix_dice: 1.555018, mix_ce: 0.181929
[15:06:55.348] iteration 68: loss: 0.936971, mix_dice: 1.549313, mix_ce: 0.324629
[15:06:55.409] iteration 69: loss: 0.857425, mix_dice: 1.530115, mix_ce: 0.184734
[15:06:55.467] iteration 70: loss: 0.912475, mix_dice: 1.515163, mix_ce: 0.309787
[15:06:55.526] iteration 71: loss: 0.900579, mix_dice: 1.605586, mix_ce: 0.195572
[15:06:56.038] iteration 72: loss: 0.904898, mix_dice: 1.564295, mix_ce: 0.245501
[15:06:56.105] iteration 73: loss: 0.915608, mix_dice: 1.582861, mix_ce: 0.248354
[15:06:56.155] iteration 74: loss: 0.877872, mix_dice: 1.496457, mix_ce: 0.259287
[15:06:56.213] iteration 75: loss: 0.940021, mix_dice: 1.485730, mix_ce: 0.394313
[15:06:56.640] iteration 76: loss: 0.942581, mix_dice: 1.563130, mix_ce: 0.322031
[15:06:56.693] iteration 77: loss: 0.892944, mix_dice: 1.526969, mix_ce: 0.258919
[15:06:56.743] iteration 78: loss: 0.933285, mix_dice: 1.612204, mix_ce: 0.254366
[15:06:56.812] iteration 79: loss: 0.934678, mix_dice: 1.462969, mix_ce: 0.406388
[15:06:57.273] iteration 80: loss: 0.910353, mix_dice: 1.556249, mix_ce: 0.264458
[15:06:57.322] iteration 81: loss: 0.901784, mix_dice: 1.469046, mix_ce: 0.334522
[15:06:57.370] iteration 82: loss: 0.858599, mix_dice: 1.433403, mix_ce: 0.283794
[15:06:57.418] iteration 83: loss: 0.880607, mix_dice: 1.499200, mix_ce: 0.262015
[15:06:57.863] iteration 84: loss: 0.848857, mix_dice: 1.467920, mix_ce: 0.229794
[15:06:58.904] iteration 85: loss: 0.940008, mix_dice: 1.506357, mix_ce: 0.373660
[15:06:58.964] iteration 86: loss: 0.884454, mix_dice: 1.573233, mix_ce: 0.195676
[15:06:59.033] iteration 87: loss: 0.865863, mix_dice: 1.464721, mix_ce: 0.267005
[15:06:59.115] iteration 88: loss: 0.887789, mix_dice: 1.490380, mix_ce: 0.285197
[15:06:59.478] iteration 89: loss: 0.908443, mix_dice: 1.547998, mix_ce: 0.268888
[15:06:59.528] iteration 90: loss: 0.969622, mix_dice: 1.524820, mix_ce: 0.414423
[15:06:59.591] iteration 91: loss: 0.891997, mix_dice: 1.495867, mix_ce: 0.288127
[15:06:59.664] iteration 92: loss: 0.870153, mix_dice: 1.457638, mix_ce: 0.282668
[15:07:00.121] iteration 93: loss: 0.902476, mix_dice: 1.585790, mix_ce: 0.219162
[15:07:00.169] iteration 94: loss: 0.912959, mix_dice: 1.538036, mix_ce: 0.287883
[15:07:00.218] iteration 95: loss: 0.838764, mix_dice: 1.417584, mix_ce: 0.259943
[15:07:00.284] iteration 96: loss: 0.838860, mix_dice: 1.510876, mix_ce: 0.166843
[15:07:00.707] iteration 97: loss: 0.861677, mix_dice: 1.482639, mix_ce: 0.240714
[15:07:00.765] iteration 98: loss: 0.923349, mix_dice: 1.589903, mix_ce: 0.256795
[15:07:00.814] iteration 99: loss: 0.913462, mix_dice: 1.570351, mix_ce: 0.256574
[15:07:00.863] iteration 100: loss: 0.821780, mix_dice: 1.499744, mix_ce: 0.143816
[15:07:01.334] iteration 101: loss: 0.891269, mix_dice: 1.506861, mix_ce: 0.275676
[15:07:01.390] iteration 102: loss: 0.917681, mix_dice: 1.586584, mix_ce: 0.248778
[15:07:01.447] iteration 103: loss: 0.844966, mix_dice: 1.522302, mix_ce: 0.167630
[15:07:01.495] iteration 104: loss: 0.943539, mix_dice: 1.631006, mix_ce: 0.256072
[15:07:01.970] iteration 105: loss: 0.871979, mix_dice: 1.531359, mix_ce: 0.212599
[15:07:02.872] iteration 106: loss: 0.898149, mix_dice: 1.478508, mix_ce: 0.317790
[15:07:02.920] iteration 107: loss: 0.954079, mix_dice: 1.614713, mix_ce: 0.293444
[15:07:02.969] iteration 108: loss: 0.873752, mix_dice: 1.477071, mix_ce: 0.270434
[15:07:03.017] iteration 109: loss: 0.874888, mix_dice: 1.602768, mix_ce: 0.147009
[15:07:03.419] iteration 110: loss: 0.883909, mix_dice: 1.490978, mix_ce: 0.276840
[15:07:03.470] iteration 111: loss: 0.878365, mix_dice: 1.575336, mix_ce: 0.181394
[15:07:03.528] iteration 112: loss: 0.879968, mix_dice: 1.502064, mix_ce: 0.257872
[15:07:03.577] iteration 113: loss: 0.853811, mix_dice: 1.516999, mix_ce: 0.190624
[15:07:04.114] iteration 114: loss: 0.871109, mix_dice: 1.522307, mix_ce: 0.219911
[15:07:04.176] iteration 115: loss: 0.850998, mix_dice: 1.536303, mix_ce: 0.165693
[15:07:04.234] iteration 116: loss: 0.898332, mix_dice: 1.555836, mix_ce: 0.240827
[15:07:04.288] iteration 117: loss: 0.860984, mix_dice: 1.441890, mix_ce: 0.280078
[15:07:04.804] iteration 118: loss: 0.936228, mix_dice: 1.553593, mix_ce: 0.318862
[15:07:04.852] iteration 119: loss: 0.898569, mix_dice: 1.483803, mix_ce: 0.313335
[15:07:04.900] iteration 120: loss: 0.893768, mix_dice: 1.511597, mix_ce: 0.275939
[15:07:04.947] iteration 121: loss: 0.952587, mix_dice: 1.555706, mix_ce: 0.349469
[15:07:05.407] iteration 122: loss: 0.916037, mix_dice: 1.645868, mix_ce: 0.186206
[15:07:05.465] iteration 123: loss: 0.962892, mix_dice: 1.609749, mix_ce: 0.316035
[15:07:05.514] iteration 124: loss: 0.846817, mix_dice: 1.468738, mix_ce: 0.224895
[15:07:05.563] iteration 125: loss: 0.907964, mix_dice: 1.505576, mix_ce: 0.310352
[15:07:06.045] iteration 126: loss: 0.871741, mix_dice: 1.511834, mix_ce: 0.231649
[15:07:06.886] iteration 127: loss: 0.854875, mix_dice: 1.506720, mix_ce: 0.203029
[15:07:06.937] iteration 128: loss: 0.860301, mix_dice: 1.498069, mix_ce: 0.222532
[15:07:06.988] iteration 129: loss: 0.909214, mix_dice: 1.616654, mix_ce: 0.201773
[15:07:07.040] iteration 130: loss: 0.866041, mix_dice: 1.482997, mix_ce: 0.249085
[15:07:07.438] iteration 131: loss: 0.849354, mix_dice: 1.458996, mix_ce: 0.239711
[15:07:07.489] iteration 132: loss: 0.866356, mix_dice: 1.554672, mix_ce: 0.178040
[15:07:07.546] iteration 133: loss: 0.864181, mix_dice: 1.474938, mix_ce: 0.253424
[15:07:07.601] iteration 134: loss: 0.934282, mix_dice: 1.539095, mix_ce: 0.329470
[15:07:08.096] iteration 135: loss: 0.892848, mix_dice: 1.548767, mix_ce: 0.236929
[15:07:08.209] iteration 136: loss: 0.925301, mix_dice: 1.591518, mix_ce: 0.259084
[15:07:08.261] iteration 137: loss: 0.862356, mix_dice: 1.468360, mix_ce: 0.256352
[15:07:08.311] iteration 138: loss: 0.889333, mix_dice: 1.608597, mix_ce: 0.170068
[15:07:08.681] iteration 139: loss: 0.898101, mix_dice: 1.522769, mix_ce: 0.273433
[15:07:08.739] iteration 140: loss: 0.905636, mix_dice: 1.515787, mix_ce: 0.295485
[15:07:08.787] iteration 141: loss: 0.946310, mix_dice: 1.732557, mix_ce: 0.160063
[15:07:08.835] iteration 142: loss: 0.869013, mix_dice: 1.493171, mix_ce: 0.244855
[15:07:09.258] iteration 143: loss: 0.955156, mix_dice: 1.617499, mix_ce: 0.292813
[15:07:09.307] iteration 144: loss: 0.850995, mix_dice: 1.459566, mix_ce: 0.242424
[15:07:09.355] iteration 145: loss: 0.896818, mix_dice: 1.572170, mix_ce: 0.221465
[15:07:09.402] iteration 146: loss: 0.875766, mix_dice: 1.478134, mix_ce: 0.273398
[15:07:09.860] iteration 147: loss: 0.853207, mix_dice: 1.516675, mix_ce: 0.189740
[15:07:10.716] iteration 148: loss: 0.846848, mix_dice: 1.459303, mix_ce: 0.234394
[15:07:10.778] iteration 149: loss: 0.866504, mix_dice: 1.433291, mix_ce: 0.299717
[15:07:10.829] iteration 150: loss: 0.887559, mix_dice: 1.473935, mix_ce: 0.301182
[15:07:10.888] iteration 151: loss: 0.941372, mix_dice: 1.665689, mix_ce: 0.217054
[15:07:11.307] iteration 152: loss: 0.870405, mix_dice: 1.472248, mix_ce: 0.268561
[15:07:11.357] iteration 153: loss: 0.881875, mix_dice: 1.482998, mix_ce: 0.280752
[15:07:11.404] iteration 154: loss: 0.905293, mix_dice: 1.583042, mix_ce: 0.227543
[15:07:11.460] iteration 155: loss: 0.880218, mix_dice: 1.553879, mix_ce: 0.206558
[15:07:12.015] iteration 156: loss: 0.888987, mix_dice: 1.584854, mix_ce: 0.193119
[15:07:12.063] iteration 157: loss: 0.863289, mix_dice: 1.500878, mix_ce: 0.225700
[15:07:12.111] iteration 158: loss: 0.902939, mix_dice: 1.468664, mix_ce: 0.337214
[15:07:12.160] iteration 159: loss: 0.911561, mix_dice: 1.509216, mix_ce: 0.313905
[15:07:12.673] iteration 160: loss: 0.908285, mix_dice: 1.527659, mix_ce: 0.288910
[15:07:12.722] iteration 161: loss: 0.901287, mix_dice: 1.505789, mix_ce: 0.296786
[15:07:12.770] iteration 162: loss: 0.864299, mix_dice: 1.521433, mix_ce: 0.207165
[15:07:12.816] iteration 163: loss: 0.861182, mix_dice: 1.481329, mix_ce: 0.241035
[15:07:13.268] iteration 164: loss: 0.853626, mix_dice: 1.427442, mix_ce: 0.279810
[15:07:13.316] iteration 165: loss: 0.835650, mix_dice: 1.484337, mix_ce: 0.186962
[15:07:13.378] iteration 166: loss: 0.823147, mix_dice: 1.444482, mix_ce: 0.201811
[15:07:13.428] iteration 167: loss: 0.816109, mix_dice: 1.448557, mix_ce: 0.183660
[15:07:13.843] iteration 168: loss: 0.904263, mix_dice: 1.641062, mix_ce: 0.167463
[15:07:14.674] iteration 169: loss: 0.931432, mix_dice: 1.545050, mix_ce: 0.317813
[15:07:14.754] iteration 170: loss: 0.859493, mix_dice: 1.459625, mix_ce: 0.259361
[15:07:14.804] iteration 171: loss: 0.861460, mix_dice: 1.510001, mix_ce: 0.212920
[15:07:14.855] iteration 172: loss: 0.908066, mix_dice: 1.642395, mix_ce: 0.173738
[15:07:15.225] iteration 173: loss: 0.872686, mix_dice: 1.524952, mix_ce: 0.220419
[15:07:15.388] iteration 174: loss: 0.896211, mix_dice: 1.637151, mix_ce: 0.155272
[15:07:15.442] iteration 175: loss: 0.965807, mix_dice: 1.514600, mix_ce: 0.417013
[15:07:15.494] iteration 176: loss: 0.870149, mix_dice: 1.463293, mix_ce: 0.277005
[15:07:15.892] iteration 177: loss: 0.830228, mix_dice: 1.478850, mix_ce: 0.181606
[15:07:15.996] iteration 178: loss: 0.834948, mix_dice: 1.477337, mix_ce: 0.192560
[15:07:16.047] iteration 179: loss: 0.893574, mix_dice: 1.531176, mix_ce: 0.255973
[15:07:16.100] iteration 180: loss: 0.835008, mix_dice: 1.467945, mix_ce: 0.202072
[15:07:16.498] iteration 181: loss: 0.915295, mix_dice: 1.507501, mix_ce: 0.323088
[15:07:16.552] iteration 182: loss: 0.853157, mix_dice: 1.507330, mix_ce: 0.198984
[15:07:16.602] iteration 183: loss: 0.904302, mix_dice: 1.603128, mix_ce: 0.205475
[15:07:16.649] iteration 184: loss: 0.802115, mix_dice: 1.466429, mix_ce: 0.137800
[15:07:17.113] iteration 185: loss: 0.897786, mix_dice: 1.609783, mix_ce: 0.185789
[15:07:17.160] iteration 186: loss: 0.847629, mix_dice: 1.511197, mix_ce: 0.184061
[15:07:17.206] iteration 187: loss: 0.870717, mix_dice: 1.452506, mix_ce: 0.288927
[15:07:17.254] iteration 188: loss: 0.899102, mix_dice: 1.486652, mix_ce: 0.311553
[15:07:17.692] iteration 189: loss: 0.926535, mix_dice: 1.636006, mix_ce: 0.217065
[15:07:18.697] iteration 190: loss: 0.825853, mix_dice: 1.479225, mix_ce: 0.172480
[15:07:18.748] iteration 191: loss: 0.887614, mix_dice: 1.478714, mix_ce: 0.296515
[15:07:18.797] iteration 192: loss: 0.898760, mix_dice: 1.561737, mix_ce: 0.235784
[15:07:18.846] iteration 193: loss: 0.867106, mix_dice: 1.572247, mix_ce: 0.161965
[15:07:19.267] iteration 194: loss: 0.875527, mix_dice: 1.565860, mix_ce: 0.185194
[15:07:19.318] iteration 195: loss: 0.857974, mix_dice: 1.504107, mix_ce: 0.211842
[15:07:19.368] iteration 196: loss: 0.850678, mix_dice: 1.498360, mix_ce: 0.202995
[15:07:19.418] iteration 197: loss: 0.896070, mix_dice: 1.527572, mix_ce: 0.264568
[15:07:19.913] iteration 198: loss: 0.878771, mix_dice: 1.516062, mix_ce: 0.241481
[15:07:19.967] iteration 199: loss: 0.880199, mix_dice: 1.504553, mix_ce: 0.255845
[15:07:20.019] iteration 200: loss: 0.889437, mix_dice: 1.536674, mix_ce: 0.242200
[15:07:20.074] iteration 201: loss: 0.928115, mix_dice: 1.705550, mix_ce: 0.150681
[15:07:20.553] iteration 202: loss: 0.891765, mix_dice: 1.590430, mix_ce: 0.193101
[15:07:20.602] iteration 203: loss: 0.881843, mix_dice: 1.558813, mix_ce: 0.204874
[15:07:20.652] iteration 204: loss: 0.853672, mix_dice: 1.482059, mix_ce: 0.225285
[15:07:20.703] iteration 205: loss: 0.828194, mix_dice: 1.513543, mix_ce: 0.142845
[15:07:21.131] iteration 206: loss: 0.854676, mix_dice: 1.476097, mix_ce: 0.233255
[15:07:21.179] iteration 207: loss: 0.894753, mix_dice: 1.505804, mix_ce: 0.283702
[15:07:21.226] iteration 208: loss: 0.885433, mix_dice: 1.530949, mix_ce: 0.239917
[15:07:21.274] iteration 209: loss: 0.913471, mix_dice: 1.565721, mix_ce: 0.261220
[15:07:21.745] iteration 210: loss: 0.883401, mix_dice: 1.644080, mix_ce: 0.122722
[15:07:22.628] iteration 211: loss: 0.865589, mix_dice: 1.495533, mix_ce: 0.235645
[15:07:22.676] iteration 212: loss: 0.856509, mix_dice: 1.526023, mix_ce: 0.186995
[15:07:22.725] iteration 213: loss: 0.906943, mix_dice: 1.603984, mix_ce: 0.209902
[15:07:22.774] iteration 214: loss: 0.890628, mix_dice: 1.562531, mix_ce: 0.218725
[15:07:23.157] iteration 215: loss: 0.906525, mix_dice: 1.521170, mix_ce: 0.291880
[15:07:23.294] iteration 216: loss: 0.816170, mix_dice: 1.448259, mix_ce: 0.184080
[15:07:23.344] iteration 217: loss: 0.820488, mix_dice: 1.457175, mix_ce: 0.183801
[15:07:23.394] iteration 218: loss: 0.862639, mix_dice: 1.521986, mix_ce: 0.203291
[15:07:23.793] iteration 219: loss: 0.947167, mix_dice: 1.645633, mix_ce: 0.248701
[15:07:23.869] iteration 220: loss: 0.946720, mix_dice: 1.531243, mix_ce: 0.362197
[15:07:23.920] iteration 221: loss: 0.787134, mix_dice: 1.406926, mix_ce: 0.167342
[15:07:23.969] iteration 222: loss: 0.908638, mix_dice: 1.514928, mix_ce: 0.302349
[15:07:24.406] iteration 223: loss: 0.876958, mix_dice: 1.499679, mix_ce: 0.254238
[15:07:24.455] iteration 224: loss: 0.833393, mix_dice: 1.408326, mix_ce: 0.258461
[15:07:24.507] iteration 225: loss: 0.891245, mix_dice: 1.596618, mix_ce: 0.185871
[15:07:24.555] iteration 226: loss: 0.853666, mix_dice: 1.515846, mix_ce: 0.191487
[15:07:25.013] iteration 227: loss: 0.874230, mix_dice: 1.487288, mix_ce: 0.261172
[15:07:25.062] iteration 228: loss: 0.848618, mix_dice: 1.483385, mix_ce: 0.213851
[15:07:25.132] iteration 229: loss: 0.840215, mix_dice: 1.465430, mix_ce: 0.215000
[15:07:25.179] iteration 230: loss: 0.895365, mix_dice: 1.595370, mix_ce: 0.195360
[15:07:25.673] iteration 231: loss: 0.874494, mix_dice: 1.525334, mix_ce: 0.223654
[15:07:26.612] iteration 232: loss: 0.877322, mix_dice: 1.667788, mix_ce: 0.086855
[15:07:26.663] iteration 233: loss: 0.872449, mix_dice: 1.570961, mix_ce: 0.173937
[15:07:26.715] iteration 234: loss: 0.926040, mix_dice: 1.596134, mix_ce: 0.255945
[15:07:26.766] iteration 235: loss: 0.944647, mix_dice: 1.616594, mix_ce: 0.272700
[15:07:27.232] iteration 236: loss: 0.854653, mix_dice: 1.540981, mix_ce: 0.168325
[15:07:27.298] iteration 237: loss: 0.807666, mix_dice: 1.478605, mix_ce: 0.136727
[15:07:27.370] iteration 238: loss: 0.886045, mix_dice: 1.483985, mix_ce: 0.288106
[15:07:27.435] iteration 239: loss: 0.961240, mix_dice: 1.595852, mix_ce: 0.326628
[15:07:27.860] iteration 240: loss: 0.933929, mix_dice: 1.557882, mix_ce: 0.309977
[15:07:27.908] iteration 241: loss: 0.892959, mix_dice: 1.558552, mix_ce: 0.227366
[15:07:27.955] iteration 242: loss: 0.929636, mix_dice: 1.577238, mix_ce: 0.282034
[15:07:28.003] iteration 243: loss: 0.871203, mix_dice: 1.511294, mix_ce: 0.231112
[15:07:28.502] iteration 244: loss: 0.881519, mix_dice: 1.593809, mix_ce: 0.169228
[15:07:28.550] iteration 245: loss: 0.861240, mix_dice: 1.487078, mix_ce: 0.235402
[15:07:28.598] iteration 246: loss: 0.817689, mix_dice: 1.428635, mix_ce: 0.206742
[15:07:28.659] iteration 247: loss: 0.853783, mix_dice: 1.537633, mix_ce: 0.169933
[15:07:29.151] iteration 248: loss: 0.862902, mix_dice: 1.457875, mix_ce: 0.267928
[15:07:29.199] iteration 249: loss: 0.893512, mix_dice: 1.599425, mix_ce: 0.187600
[15:07:29.249] iteration 250: loss: 0.891196, mix_dice: 1.527066, mix_ce: 0.255325
[15:07:29.297] iteration 251: loss: 0.859070, mix_dice: 1.474087, mix_ce: 0.244053
[15:07:29.742] iteration 252: loss: 0.813998, mix_dice: 1.440144, mix_ce: 0.187853
[15:07:30.675] iteration 253: loss: 0.850784, mix_dice: 1.575881, mix_ce: 0.125686
[15:07:30.726] iteration 254: loss: 0.858768, mix_dice: 1.464633, mix_ce: 0.252903
[15:07:30.773] iteration 255: loss: 0.892901, mix_dice: 1.635186, mix_ce: 0.150616
[15:07:30.821] iteration 256: loss: 0.907008, mix_dice: 1.719518, mix_ce: 0.094498
[15:07:31.258] iteration 257: loss: 0.927400, mix_dice: 1.561613, mix_ce: 0.293187
[15:07:31.308] iteration 258: loss: 0.908276, mix_dice: 1.703979, mix_ce: 0.112572
[15:07:31.358] iteration 259: loss: 0.833730, mix_dice: 1.530478, mix_ce: 0.136981
[15:07:31.406] iteration 260: loss: 0.851078, mix_dice: 1.537283, mix_ce: 0.164873
[15:07:31.916] iteration 261: loss: 0.832811, mix_dice: 1.479467, mix_ce: 0.186156
[15:07:31.965] iteration 262: loss: 0.884798, mix_dice: 1.484656, mix_ce: 0.284941
[15:07:32.024] iteration 263: loss: 0.884737, mix_dice: 1.489437, mix_ce: 0.280037
[15:07:32.081] iteration 264: loss: 0.839888, mix_dice: 1.478959, mix_ce: 0.200817
[15:07:32.568] iteration 265: loss: 0.823441, mix_dice: 1.423733, mix_ce: 0.223149
[15:07:32.620] iteration 266: loss: 0.839957, mix_dice: 1.464050, mix_ce: 0.215864
[15:07:32.673] iteration 267: loss: 0.836032, mix_dice: 1.428715, mix_ce: 0.243348
[15:07:32.722] iteration 268: loss: 0.843778, mix_dice: 1.488396, mix_ce: 0.199160
[15:07:33.160] iteration 269: loss: 0.899821, mix_dice: 1.473255, mix_ce: 0.326386
[15:07:33.214] iteration 270: loss: 0.875988, mix_dice: 1.479186, mix_ce: 0.272790
[15:07:33.265] iteration 271: loss: 0.837909, mix_dice: 1.436195, mix_ce: 0.239622
[15:07:33.317] iteration 272: loss: 0.936193, mix_dice: 1.534327, mix_ce: 0.338059
[15:07:33.811] iteration 273: loss: 0.925858, mix_dice: 1.498070, mix_ce: 0.353646
[15:07:34.790] iteration 274: loss: 0.889114, mix_dice: 1.496930, mix_ce: 0.281297
[15:07:34.838] iteration 275: loss: 0.892173, mix_dice: 1.599838, mix_ce: 0.184507
[15:07:34.887] iteration 276: loss: 0.866429, mix_dice: 1.466174, mix_ce: 0.266684
[15:07:34.936] iteration 277: loss: 0.863236, mix_dice: 1.467609, mix_ce: 0.258862
[15:07:35.338] iteration 278: loss: 0.837101, mix_dice: 1.464658, mix_ce: 0.209543
[15:07:35.386] iteration 279: loss: 0.890013, mix_dice: 1.528254, mix_ce: 0.251773
[15:07:35.434] iteration 280: loss: 0.853200, mix_dice: 1.547004, mix_ce: 0.159396
[15:07:35.485] iteration 281: loss: 0.880954, mix_dice: 1.485623, mix_ce: 0.276285
[15:07:36.059] iteration 282: loss: 0.868941, mix_dice: 1.492004, mix_ce: 0.245877
[15:07:36.110] iteration 283: loss: 0.816644, mix_dice: 1.416191, mix_ce: 0.217098
[15:07:36.161] iteration 284: loss: 0.835921, mix_dice: 1.515573, mix_ce: 0.156270
[15:07:36.213] iteration 285: loss: 0.872467, mix_dice: 1.528312, mix_ce: 0.216623
[15:07:36.700] iteration 286: loss: 0.907616, mix_dice: 1.478194, mix_ce: 0.337039
[15:07:36.751] iteration 287: loss: 0.887579, mix_dice: 1.488295, mix_ce: 0.286864
[15:07:36.802] iteration 288: loss: 0.791294, mix_dice: 1.426899, mix_ce: 0.155689
[15:07:36.854] iteration 289: loss: 0.878229, mix_dice: 1.563307, mix_ce: 0.193150
[15:07:37.326] iteration 290: loss: 0.870196, mix_dice: 1.585895, mix_ce: 0.154497
[15:07:37.373] iteration 291: loss: 0.929014, mix_dice: 1.640626, mix_ce: 0.217402
[15:07:37.421] iteration 292: loss: 0.850661, mix_dice: 1.533590, mix_ce: 0.167732
[15:07:37.469] iteration 293: loss: 0.815867, mix_dice: 1.403516, mix_ce: 0.228217
[15:07:37.931] iteration 294: loss: 0.866850, mix_dice: 1.523564, mix_ce: 0.210137
[15:07:38.766] iteration 295: loss: 0.851306, mix_dice: 1.463885, mix_ce: 0.238726
[15:07:38.815] iteration 296: loss: 0.865517, mix_dice: 1.539313, mix_ce: 0.191721
[15:07:38.866] iteration 297: loss: 0.844688, mix_dice: 1.477976, mix_ce: 0.211400
[15:07:38.915] iteration 298: loss: 0.843507, mix_dice: 1.451902, mix_ce: 0.235111
[15:07:39.310] iteration 299: loss: 0.872373, mix_dice: 1.516144, mix_ce: 0.228601
[15:07:39.359] iteration 300: loss: 0.797609, mix_dice: 1.357567, mix_ce: 0.237651
[15:07:39.410] iteration 301: loss: 0.831741, mix_dice: 1.443927, mix_ce: 0.219555
[15:07:39.458] iteration 302: loss: 0.876655, mix_dice: 1.619583, mix_ce: 0.133726
[15:07:39.969] iteration 303: loss: 0.817373, mix_dice: 1.467581, mix_ce: 0.167166
[15:07:40.016] iteration 304: loss: 0.833142, mix_dice: 1.407172, mix_ce: 0.259111
[15:07:40.065] iteration 305: loss: 0.834198, mix_dice: 1.400796, mix_ce: 0.267600
[15:07:40.114] iteration 306: loss: 0.841481, mix_dice: 1.436373, mix_ce: 0.246588
[15:07:40.564] iteration 307: loss: 0.863470, mix_dice: 1.531949, mix_ce: 0.194991
[15:07:40.614] iteration 308: loss: 0.840440, mix_dice: 1.537146, mix_ce: 0.143733
[15:07:40.664] iteration 309: loss: 0.843814, mix_dice: 1.544698, mix_ce: 0.142930
[15:07:40.713] iteration 310: loss: 0.820496, mix_dice: 1.483661, mix_ce: 0.157331
[15:07:41.149] iteration 311: loss: 0.831610, mix_dice: 1.443749, mix_ce: 0.219472
[15:07:41.202] iteration 312: loss: 0.850943, mix_dice: 1.512798, mix_ce: 0.189087
[15:07:41.250] iteration 313: loss: 0.894974, mix_dice: 1.622059, mix_ce: 0.167889
[15:07:41.299] iteration 314: loss: 0.858872, mix_dice: 1.455911, mix_ce: 0.261834
[15:07:41.719] iteration 315: loss: 0.873257, mix_dice: 1.465077, mix_ce: 0.281436
[15:07:42.533] iteration 316: loss: 0.830970, mix_dice: 1.433908, mix_ce: 0.228032
[15:07:42.585] iteration 317: loss: 0.899923, mix_dice: 1.578393, mix_ce: 0.221453
[15:07:42.635] iteration 318: loss: 0.835881, mix_dice: 1.448384, mix_ce: 0.223378
[15:07:42.683] iteration 319: loss: 0.893434, mix_dice: 1.547582, mix_ce: 0.239286
[15:07:43.050] iteration 320: loss: 0.887969, mix_dice: 1.608787, mix_ce: 0.167151
[15:07:43.129] iteration 321: loss: 0.808933, mix_dice: 1.479037, mix_ce: 0.138830
[15:07:43.176] iteration 322: loss: 0.873833, mix_dice: 1.516837, mix_ce: 0.230829
[15:07:43.236] iteration 323: loss: 0.844133, mix_dice: 1.495984, mix_ce: 0.192282
[15:07:43.743] iteration 324: loss: 0.797831, mix_dice: 1.439739, mix_ce: 0.155922
[15:07:43.791] iteration 325: loss: 0.893178, mix_dice: 1.514374, mix_ce: 0.271981
[15:07:43.838] iteration 326: loss: 0.883887, mix_dice: 1.552151, mix_ce: 0.215623
[15:07:43.885] iteration 327: loss: 0.954093, mix_dice: 1.535466, mix_ce: 0.372720
[15:07:44.451] iteration 328: loss: 0.849802, mix_dice: 1.451027, mix_ce: 0.248576
[15:07:44.500] iteration 329: loss: 0.830725, mix_dice: 1.395902, mix_ce: 0.265548
[15:07:44.548] iteration 330: loss: 0.857764, mix_dice: 1.499582, mix_ce: 0.215945
[15:07:44.596] iteration 331: loss: 0.859952, mix_dice: 1.491901, mix_ce: 0.228003
[15:07:45.076] iteration 332: loss: 0.902278, mix_dice: 1.548347, mix_ce: 0.256210
[15:07:45.129] iteration 333: loss: 0.865936, mix_dice: 1.448089, mix_ce: 0.283782
[15:07:45.183] iteration 334: loss: 0.853644, mix_dice: 1.485778, mix_ce: 0.221510
[15:07:45.241] iteration 335: loss: 0.803018, mix_dice: 1.416173, mix_ce: 0.189864
[15:07:45.698] iteration 336: loss: 0.900700, mix_dice: 1.514993, mix_ce: 0.286406
[15:07:46.620] iteration 337: loss: 0.782813, mix_dice: 1.398397, mix_ce: 0.167229
[15:07:46.674] iteration 338: loss: 0.866734, mix_dice: 1.606647, mix_ce: 0.126820
[15:07:46.732] iteration 339: loss: 0.830428, mix_dice: 1.419981, mix_ce: 0.240875
[15:07:46.790] iteration 340: loss: 0.876381, mix_dice: 1.532980, mix_ce: 0.219783
[15:07:47.163] iteration 341: loss: 0.823992, mix_dice: 1.465885, mix_ce: 0.182099
[15:07:47.222] iteration 342: loss: 0.868418, mix_dice: 1.541098, mix_ce: 0.195737
[15:07:47.276] iteration 343: loss: 0.927481, mix_dice: 1.479110, mix_ce: 0.375852
[15:07:47.336] iteration 344: loss: 0.856866, mix_dice: 1.566919, mix_ce: 0.146814
[15:07:47.768] iteration 345: loss: 0.790949, mix_dice: 1.395547, mix_ce: 0.186350
[15:07:47.817] iteration 346: loss: 0.879835, mix_dice: 1.506639, mix_ce: 0.253030
[15:07:47.868] iteration 347: loss: 0.805537, mix_dice: 1.416419, mix_ce: 0.194656
[15:07:47.922] iteration 348: loss: 0.804606, mix_dice: 1.392188, mix_ce: 0.217023
[15:07:48.358] iteration 349: loss: 0.923599, mix_dice: 1.668744, mix_ce: 0.178453
[15:07:48.410] iteration 350: loss: 0.815643, mix_dice: 1.400310, mix_ce: 0.230977
[15:07:48.463] iteration 351: loss: 0.841404, mix_dice: 1.505268, mix_ce: 0.177540
[15:07:48.516] iteration 352: loss: 0.846434, mix_dice: 1.575663, mix_ce: 0.117204
[15:07:48.917] iteration 353: loss: 0.857413, mix_dice: 1.478096, mix_ce: 0.236731
[15:07:48.966] iteration 354: loss: 0.844586, mix_dice: 1.425076, mix_ce: 0.264097
[15:07:49.016] iteration 355: loss: 0.823401, mix_dice: 1.502834, mix_ce: 0.143969
[15:07:49.064] iteration 356: loss: 0.784256, mix_dice: 1.428176, mix_ce: 0.140336
[15:07:49.468] iteration 357: loss: 0.854903, mix_dice: 1.563178, mix_ce: 0.146627
[15:07:50.330] iteration 358: loss: 0.837248, mix_dice: 1.423385, mix_ce: 0.251111
[15:07:50.379] iteration 359: loss: 0.803065, mix_dice: 1.443560, mix_ce: 0.162571
[15:07:50.428] iteration 360: loss: 0.819576, mix_dice: 1.444532, mix_ce: 0.194621
[15:07:50.474] iteration 361: loss: 0.840233, mix_dice: 1.410823, mix_ce: 0.269644
[15:07:50.856] iteration 362: loss: 0.796001, mix_dice: 1.469439, mix_ce: 0.122564
[15:07:50.961] iteration 363: loss: 0.803157, mix_dice: 1.418509, mix_ce: 0.187805
[15:07:51.010] iteration 364: loss: 0.813911, mix_dice: 1.440879, mix_ce: 0.186944
[15:07:51.061] iteration 365: loss: 0.807175, mix_dice: 1.450549, mix_ce: 0.163801
[15:07:51.467] iteration 366: loss: 0.836567, mix_dice: 1.524492, mix_ce: 0.148643
[15:07:51.547] iteration 367: loss: 0.859066, mix_dice: 1.501264, mix_ce: 0.216867
[15:07:51.597] iteration 368: loss: 0.798072, mix_dice: 1.449382, mix_ce: 0.146763
[15:07:51.665] iteration 369: loss: 0.867449, mix_dice: 1.535030, mix_ce: 0.199869
[15:07:52.068] iteration 370: loss: 0.845404, mix_dice: 1.494644, mix_ce: 0.196163
[15:07:52.120] iteration 371: loss: 0.874075, mix_dice: 1.544049, mix_ce: 0.204100
[15:07:52.170] iteration 372: loss: 0.852051, mix_dice: 1.491049, mix_ce: 0.213053
[15:07:52.232] iteration 373: loss: 0.813218, mix_dice: 1.458901, mix_ce: 0.167536
[15:07:52.600] iteration 374: loss: 0.794021, mix_dice: 1.382620, mix_ce: 0.205422
[15:07:52.648] iteration 375: loss: 0.800790, mix_dice: 1.415616, mix_ce: 0.185964
[15:07:52.695] iteration 376: loss: 0.895996, mix_dice: 1.670636, mix_ce: 0.121355
[15:07:52.798] iteration 377: loss: 0.875972, mix_dice: 1.545793, mix_ce: 0.206152
[15:07:53.159] iteration 378: loss: 0.879999, mix_dice: 1.488351, mix_ce: 0.271647
[15:07:54.003] iteration 379: loss: 0.865094, mix_dice: 1.406355, mix_ce: 0.323833
[15:07:54.053] iteration 380: loss: 0.896059, mix_dice: 1.472838, mix_ce: 0.319281
[15:07:54.102] iteration 381: loss: 0.804553, mix_dice: 1.427676, mix_ce: 0.181430
[15:07:54.151] iteration 382: loss: 0.828017, mix_dice: 1.408818, mix_ce: 0.247217
[15:07:54.586] iteration 383: loss: 0.809960, mix_dice: 1.429783, mix_ce: 0.190137
[15:07:54.634] iteration 384: loss: 0.895166, mix_dice: 1.592367, mix_ce: 0.197966
[15:07:54.682] iteration 385: loss: 0.860442, mix_dice: 1.444264, mix_ce: 0.276620
[15:07:54.728] iteration 386: loss: 0.797374, mix_dice: 1.435423, mix_ce: 0.159325
[15:07:55.263] iteration 387: loss: 0.867345, mix_dice: 1.449462, mix_ce: 0.285227
[15:07:55.321] iteration 388: loss: 0.854029, mix_dice: 1.495757, mix_ce: 0.212301
[15:07:55.373] iteration 389: loss: 0.822916, mix_dice: 1.399355, mix_ce: 0.246476
[15:07:55.422] iteration 390: loss: 0.871870, mix_dice: 1.406583, mix_ce: 0.337158
[15:07:55.856] iteration 391: loss: 0.811987, mix_dice: 1.385203, mix_ce: 0.238770
[15:07:55.904] iteration 392: loss: 0.854611, mix_dice: 1.475276, mix_ce: 0.233945
[15:07:55.953] iteration 393: loss: 0.855294, mix_dice: 1.474872, mix_ce: 0.235716
[15:07:56.000] iteration 394: loss: 0.844785, mix_dice: 1.565979, mix_ce: 0.123591
[15:07:56.417] iteration 395: loss: 0.845965, mix_dice: 1.517040, mix_ce: 0.174890
[15:07:56.470] iteration 396: loss: 0.868147, mix_dice: 1.494039, mix_ce: 0.242255
[15:07:56.528] iteration 397: loss: 0.806312, mix_dice: 1.490433, mix_ce: 0.122190
[15:07:56.598] iteration 398: loss: 0.925135, mix_dice: 1.731137, mix_ce: 0.119134
[15:07:56.985] iteration 399: loss: 0.858287, mix_dice: 1.557032, mix_ce: 0.159542
[15:07:57.854] iteration 400: loss: 0.886305, mix_dice: 1.489685, mix_ce: 0.282925
[15:07:57.915] iteration 401: loss: 0.840515, mix_dice: 1.576135, mix_ce: 0.104896
[15:07:57.977] iteration 402: loss: 0.802888, mix_dice: 1.458534, mix_ce: 0.147241
[15:07:58.037] iteration 403: loss: 0.821916, mix_dice: 1.475933, mix_ce: 0.167899
[15:07:58.408] iteration 404: loss: 0.778911, mix_dice: 1.424819, mix_ce: 0.133002
[15:07:58.457] iteration 405: loss: 0.783012, mix_dice: 1.415920, mix_ce: 0.150104
[15:07:58.505] iteration 406: loss: 0.886916, mix_dice: 1.506257, mix_ce: 0.267575
[15:07:58.552] iteration 407: loss: 0.841641, mix_dice: 1.466634, mix_ce: 0.216647
[15:07:59.098] iteration 408: loss: 0.816752, mix_dice: 1.435654, mix_ce: 0.197850
[15:07:59.148] iteration 409: loss: 0.866129, mix_dice: 1.507597, mix_ce: 0.224660
[15:07:59.197] iteration 410: loss: 0.836196, mix_dice: 1.517711, mix_ce: 0.154681
[15:07:59.248] iteration 411: loss: 0.856540, mix_dice: 1.453544, mix_ce: 0.259536
[15:07:59.743] iteration 412: loss: 0.801376, mix_dice: 1.406404, mix_ce: 0.196348
[15:07:59.809] iteration 413: loss: 0.845381, mix_dice: 1.432081, mix_ce: 0.258681
[15:07:59.873] iteration 414: loss: 0.811040, mix_dice: 1.395034, mix_ce: 0.227046
[15:07:59.924] iteration 415: loss: 0.785871, mix_dice: 1.388793, mix_ce: 0.182949
[15:08:00.362] iteration 416: loss: 0.862398, mix_dice: 1.421561, mix_ce: 0.303235
[15:08:00.408] iteration 417: loss: 0.791268, mix_dice: 1.412761, mix_ce: 0.169774
[15:08:00.455] iteration 418: loss: 0.795876, mix_dice: 1.391042, mix_ce: 0.200710
[15:08:00.503] iteration 419: loss: 0.796029, mix_dice: 1.439344, mix_ce: 0.152714
[15:08:00.982] iteration 420: loss: 0.800563, mix_dice: 1.422762, mix_ce: 0.178363
[15:08:01.779] iteration 421: loss: 0.839072, mix_dice: 1.486542, mix_ce: 0.191602
[15:08:01.842] iteration 422: loss: 0.891213, mix_dice: 1.538641, mix_ce: 0.243785
[15:08:01.890] iteration 423: loss: 0.845279, mix_dice: 1.489290, mix_ce: 0.201268
[15:08:01.938] iteration 424: loss: 0.791443, mix_dice: 1.435046, mix_ce: 0.147839
[15:08:02.283] iteration 425: loss: 0.809714, mix_dice: 1.474466, mix_ce: 0.144963
[15:08:02.409] iteration 426: loss: 0.775631, mix_dice: 1.374617, mix_ce: 0.176644
[15:08:02.458] iteration 427: loss: 0.819466, mix_dice: 1.482958, mix_ce: 0.155973
[15:08:02.505] iteration 428: loss: 0.795165, mix_dice: 1.408240, mix_ce: 0.182090
[15:08:02.910] iteration 429: loss: 0.768775, mix_dice: 1.344833, mix_ce: 0.192717
[15:08:03.020] iteration 430: loss: 0.827477, mix_dice: 1.417643, mix_ce: 0.237311
[15:08:03.073] iteration 431: loss: 0.828931, mix_dice: 1.400926, mix_ce: 0.256935
[15:08:03.126] iteration 432: loss: 0.797019, mix_dice: 1.391039, mix_ce: 0.202998
[15:08:03.499] iteration 433: loss: 0.768818, mix_dice: 1.329138, mix_ce: 0.208498
[15:08:03.553] iteration 434: loss: 0.824150, mix_dice: 1.421832, mix_ce: 0.226467
[15:08:03.602] iteration 435: loss: 0.840348, mix_dice: 1.472557, mix_ce: 0.208139
[15:08:03.652] iteration 436: loss: 0.755315, mix_dice: 1.274688, mix_ce: 0.235943
[15:08:04.114] iteration 437: loss: 0.809489, mix_dice: 1.461199, mix_ce: 0.157780
[15:08:04.163] iteration 438: loss: 0.894859, mix_dice: 1.605586, mix_ce: 0.184131
[15:08:04.210] iteration 439: loss: 0.910581, mix_dice: 1.356340, mix_ce: 0.464823
[15:08:04.256] iteration 440: loss: 0.813682, mix_dice: 1.465561, mix_ce: 0.161803
[15:08:04.722] iteration 441: loss: 0.838155, mix_dice: 1.479736, mix_ce: 0.196574
[15:08:05.537] iteration 442: loss: 0.837182, mix_dice: 1.422853, mix_ce: 0.251512
[15:08:05.585] iteration 443: loss: 0.773801, mix_dice: 1.323355, mix_ce: 0.224246
[15:08:05.633] iteration 444: loss: 0.805549, mix_dice: 1.424773, mix_ce: 0.186325
[15:08:05.682] iteration 445: loss: 0.839844, mix_dice: 1.512848, mix_ce: 0.166840
[15:08:06.073] iteration 446: loss: 0.828842, mix_dice: 1.486455, mix_ce: 0.171228
[15:08:06.122] iteration 447: loss: 0.816894, mix_dice: 1.450958, mix_ce: 0.182831
[15:08:06.172] iteration 448: loss: 0.759084, mix_dice: 1.338548, mix_ce: 0.179619
[15:08:06.220] iteration 449: loss: 0.756825, mix_dice: 1.363290, mix_ce: 0.150361
[15:08:06.683] iteration 450: loss: 0.788577, mix_dice: 1.422222, mix_ce: 0.154931
[15:08:06.730] iteration 451: loss: 0.828457, mix_dice: 1.503474, mix_ce: 0.153440
[15:08:06.778] iteration 452: loss: 0.752195, mix_dice: 1.365675, mix_ce: 0.138716
[15:08:06.838] iteration 453: loss: 0.840584, mix_dice: 1.414145, mix_ce: 0.267024
[15:08:07.291] iteration 454: loss: 0.774218, mix_dice: 1.371849, mix_ce: 0.176587
[15:08:07.346] iteration 455: loss: 0.813412, mix_dice: 1.371310, mix_ce: 0.255513
[15:08:07.400] iteration 456: loss: 0.792377, mix_dice: 1.479394, mix_ce: 0.105360
[15:08:07.452] iteration 457: loss: 0.799195, mix_dice: 1.399541, mix_ce: 0.198848
[15:08:07.867] iteration 458: loss: 0.832478, mix_dice: 1.532949, mix_ce: 0.132006
[15:08:07.920] iteration 459: loss: 0.825786, mix_dice: 1.427633, mix_ce: 0.223939
[15:08:07.969] iteration 460: loss: 0.809010, mix_dice: 1.435384, mix_ce: 0.182635
[15:08:08.044] iteration 461: loss: 0.857970, mix_dice: 1.399394, mix_ce: 0.316547
[15:08:08.432] iteration 462: loss: 0.832475, mix_dice: 1.428471, mix_ce: 0.236478
[15:08:09.257] iteration 463: loss: 0.808027, mix_dice: 1.392045, mix_ce: 0.224009
[15:08:09.333] iteration 464: loss: 0.812499, mix_dice: 1.436816, mix_ce: 0.188182
[15:08:09.392] iteration 465: loss: 0.879698, mix_dice: 1.538090, mix_ce: 0.221306
[15:08:09.452] iteration 466: loss: 0.844393, mix_dice: 1.503935, mix_ce: 0.184851
[15:08:09.785] iteration 467: loss: 0.777937, mix_dice: 1.304135, mix_ce: 0.251739
[15:08:09.840] iteration 468: loss: 0.830936, mix_dice: 1.499569, mix_ce: 0.162304
[15:08:09.889] iteration 469: loss: 0.852737, mix_dice: 1.552342, mix_ce: 0.153131
[15:08:09.940] iteration 470: loss: 0.753601, mix_dice: 1.330838, mix_ce: 0.176364
[15:08:10.412] iteration 471: loss: 0.832918, mix_dice: 1.341626, mix_ce: 0.324211
[15:08:10.458] iteration 472: loss: 0.843712, mix_dice: 1.451797, mix_ce: 0.235626
[15:08:10.509] iteration 473: loss: 0.736657, mix_dice: 1.262243, mix_ce: 0.211071
[15:08:10.560] iteration 474: loss: 0.823853, mix_dice: 1.512900, mix_ce: 0.134806
[15:08:11.017] iteration 475: loss: 0.787517, mix_dice: 1.388917, mix_ce: 0.186116
[15:08:11.071] iteration 476: loss: 0.795673, mix_dice: 1.343580, mix_ce: 0.247766
[15:08:11.118] iteration 477: loss: 0.782080, mix_dice: 1.375238, mix_ce: 0.188923
[15:08:11.164] iteration 478: loss: 0.742226, mix_dice: 1.327065, mix_ce: 0.157387
[15:08:11.617] iteration 479: loss: 0.739470, mix_dice: 1.278024, mix_ce: 0.200916
[15:08:11.665] iteration 480: loss: 0.835846, mix_dice: 1.437015, mix_ce: 0.234677
[15:08:11.712] iteration 481: loss: 0.885321, mix_dice: 1.600221, mix_ce: 0.170421
[15:08:11.760] iteration 482: loss: 0.856062, mix_dice: 1.439782, mix_ce: 0.272342
[15:08:12.183] iteration 483: loss: 0.787956, mix_dice: 1.355693, mix_ce: 0.220220
[15:08:13.042] iteration 484: loss: 0.821300, mix_dice: 1.374150, mix_ce: 0.268450
[15:08:13.097] iteration 485: loss: 0.826680, mix_dice: 1.494677, mix_ce: 0.158683
[15:08:13.149] iteration 486: loss: 0.744399, mix_dice: 1.322216, mix_ce: 0.166583
[15:08:13.197] iteration 487: loss: 0.809454, mix_dice: 1.392171, mix_ce: 0.226737
[15:08:13.575] iteration 488: loss: 0.798492, mix_dice: 1.402636, mix_ce: 0.194348
[15:08:13.626] iteration 489: loss: 0.858033, mix_dice: 1.373800, mix_ce: 0.342267
[15:08:13.677] iteration 490: loss: 0.720588, mix_dice: 1.281935, mix_ce: 0.159242
[15:08:13.726] iteration 491: loss: 0.815305, mix_dice: 1.496662, mix_ce: 0.133948
[15:08:14.202] iteration 492: loss: 0.765708, mix_dice: 1.305658, mix_ce: 0.225757
[15:08:14.258] iteration 493: loss: 0.777247, mix_dice: 1.349929, mix_ce: 0.204565
[15:08:14.310] iteration 494: loss: 0.730224, mix_dice: 1.271369, mix_ce: 0.189078
[15:08:14.359] iteration 495: loss: 0.770894, mix_dice: 1.362251, mix_ce: 0.179538
[15:08:14.811] iteration 496: loss: 0.808371, mix_dice: 1.349565, mix_ce: 0.267177
[15:08:14.878] iteration 497: loss: 0.926281, mix_dice: 1.483544, mix_ce: 0.369018
[15:08:14.930] iteration 498: loss: 0.746842, mix_dice: 1.330899, mix_ce: 0.162785
[15:08:14.995] iteration 499: loss: 0.816519, mix_dice: 1.409682, mix_ce: 0.223356
[15:08:15.417] iteration 500: loss: 0.731642, mix_dice: 1.307248, mix_ce: 0.156035
[15:08:15.470] iteration 501: loss: 0.874033, mix_dice: 1.508927, mix_ce: 0.239138
[15:08:15.523] iteration 502: loss: 0.850140, mix_dice: 1.388577, mix_ce: 0.311703
[15:08:15.577] iteration 503: loss: 0.854882, mix_dice: 1.562923, mix_ce: 0.146842
[15:08:15.983] iteration 504: loss: 0.820846, mix_dice: 1.480600, mix_ce: 0.161092
[15:08:16.834] iteration 505: loss: 0.799650, mix_dice: 1.466018, mix_ce: 0.133282
[15:08:16.899] iteration 506: loss: 0.774365, mix_dice: 1.330236, mix_ce: 0.218494
[15:08:16.953] iteration 507: loss: 0.803057, mix_dice: 1.352564, mix_ce: 0.253550
[15:08:17.012] iteration 508: loss: 0.783875, mix_dice: 1.464279, mix_ce: 0.103472
[15:08:17.367] iteration 509: loss: 0.726254, mix_dice: 1.207826, mix_ce: 0.244682
[15:08:17.463] iteration 510: loss: 0.723303, mix_dice: 1.245972, mix_ce: 0.200634
[15:08:17.527] iteration 511: loss: 0.849392, mix_dice: 1.408114, mix_ce: 0.290670
[15:08:17.587] iteration 512: loss: 0.787713, mix_dice: 1.387605, mix_ce: 0.187821
[15:08:18.104] iteration 513: loss: 0.756425, mix_dice: 1.270915, mix_ce: 0.241935
[15:08:18.199] iteration 514: loss: 0.830751, mix_dice: 1.499227, mix_ce: 0.162275
[15:08:18.283] iteration 515: loss: 0.765794, mix_dice: 1.324442, mix_ce: 0.207146
[15:08:18.334] iteration 516: loss: 0.736143, mix_dice: 1.287403, mix_ce: 0.184883
[15:08:18.716] iteration 517: loss: 0.778423, mix_dice: 1.365164, mix_ce: 0.191683
[15:08:18.778] iteration 518: loss: 0.791601, mix_dice: 1.429238, mix_ce: 0.153964
[15:08:18.837] iteration 519: loss: 0.802407, mix_dice: 1.314187, mix_ce: 0.290628
[15:08:18.893] iteration 520: loss: 0.784673, mix_dice: 1.343677, mix_ce: 0.225670
[15:08:19.314] iteration 521: loss: 0.775562, mix_dice: 1.357099, mix_ce: 0.194025
[15:08:19.363] iteration 522: loss: 0.738012, mix_dice: 1.320255, mix_ce: 0.155770
[15:08:19.412] iteration 523: loss: 0.838343, mix_dice: 1.452083, mix_ce: 0.224602
[15:08:19.462] iteration 524: loss: 0.829308, mix_dice: 1.462002, mix_ce: 0.196615
[15:08:19.922] iteration 525: loss: 0.794063, mix_dice: 1.386415, mix_ce: 0.201711
[15:08:20.754] iteration 526: loss: 0.757350, mix_dice: 1.329884, mix_ce: 0.184817
[15:08:20.805] iteration 527: loss: 0.887715, mix_dice: 1.571497, mix_ce: 0.203932
[15:08:20.855] iteration 528: loss: 0.832995, mix_dice: 1.468171, mix_ce: 0.197818
[15:08:20.904] iteration 529: loss: 0.798439, mix_dice: 1.416933, mix_ce: 0.179945
[15:08:21.298] iteration 530: loss: 0.790338, mix_dice: 1.370048, mix_ce: 0.210628
[15:08:21.352] iteration 531: loss: 0.919165, mix_dice: 1.527782, mix_ce: 0.310548
[15:08:21.403] iteration 532: loss: 0.741826, mix_dice: 1.380002, mix_ce: 0.103650
[15:08:21.453] iteration 533: loss: 0.761257, mix_dice: 1.369505, mix_ce: 0.153010
[15:08:21.992] iteration 534: loss: 0.733645, mix_dice: 1.267302, mix_ce: 0.199989
[15:08:22.043] iteration 535: loss: 0.819392, mix_dice: 1.482147, mix_ce: 0.156637
[15:08:22.095] iteration 536: loss: 0.823395, mix_dice: 1.418092, mix_ce: 0.228698
[15:08:22.149] iteration 537: loss: 0.813483, mix_dice: 1.355973, mix_ce: 0.270992
[15:08:22.590] iteration 538: loss: 0.815780, mix_dice: 1.354039, mix_ce: 0.277521
[15:08:22.640] iteration 539: loss: 0.782089, mix_dice: 1.331735, mix_ce: 0.232443
[15:08:22.688] iteration 540: loss: 0.749331, mix_dice: 1.264209, mix_ce: 0.234452
[15:08:22.736] iteration 541: loss: 0.769308, mix_dice: 1.259583, mix_ce: 0.279033
[15:08:23.190] iteration 542: loss: 0.872528, mix_dice: 1.538361, mix_ce: 0.206695
[15:08:23.238] iteration 543: loss: 0.749094, mix_dice: 1.281731, mix_ce: 0.216457
[15:08:23.287] iteration 544: loss: 0.743728, mix_dice: 1.301296, mix_ce: 0.186160
[15:08:23.336] iteration 545: loss: 0.843945, mix_dice: 1.462608, mix_ce: 0.225281
[15:08:23.787] iteration 546: loss: 0.791675, mix_dice: 1.371248, mix_ce: 0.212103
[15:08:24.675] iteration 547: loss: 0.745297, mix_dice: 1.387788, mix_ce: 0.102807
[15:08:24.722] iteration 548: loss: 0.737950, mix_dice: 1.272715, mix_ce: 0.203184
[15:08:24.770] iteration 549: loss: 0.783623, mix_dice: 1.387337, mix_ce: 0.179908
[15:08:24.817] iteration 550: loss: 0.732104, mix_dice: 1.285754, mix_ce: 0.178455
[15:08:25.213] iteration 551: loss: 0.823404, mix_dice: 1.387486, mix_ce: 0.259323
[15:08:25.265] iteration 552: loss: 0.751166, mix_dice: 1.253154, mix_ce: 0.249179
[15:08:25.317] iteration 553: loss: 0.760960, mix_dice: 1.341764, mix_ce: 0.180157
[15:08:25.370] iteration 554: loss: 0.716943, mix_dice: 1.213603, mix_ce: 0.220284
[15:08:25.860] iteration 555: loss: 0.717115, mix_dice: 1.211374, mix_ce: 0.222857
[15:08:25.910] iteration 556: loss: 0.717156, mix_dice: 1.291584, mix_ce: 0.142727
[15:08:25.961] iteration 557: loss: 0.705338, mix_dice: 1.146082, mix_ce: 0.264595
[15:08:26.010] iteration 558: loss: 0.871098, mix_dice: 1.532101, mix_ce: 0.210094
[15:08:26.444] iteration 559: loss: 0.829435, mix_dice: 1.431383, mix_ce: 0.227487
[15:08:26.495] iteration 560: loss: 0.747462, mix_dice: 1.313608, mix_ce: 0.181315
[15:08:26.564] iteration 561: loss: 0.806496, mix_dice: 1.452962, mix_ce: 0.160029
[15:08:26.612] iteration 562: loss: 0.749000, mix_dice: 1.281858, mix_ce: 0.216143
[15:08:27.019] iteration 563: loss: 0.820612, mix_dice: 1.478926, mix_ce: 0.162298
[15:08:27.068] iteration 564: loss: 0.687253, mix_dice: 1.212124, mix_ce: 0.162382
[15:08:27.115] iteration 565: loss: 0.843303, mix_dice: 1.443545, mix_ce: 0.243061
[15:08:27.164] iteration 566: loss: 0.775573, mix_dice: 1.410158, mix_ce: 0.140988
[15:08:27.567] iteration 567: loss: 0.760346, mix_dice: 1.318136, mix_ce: 0.202555
[15:08:28.453] iteration 568: loss: 0.714774, mix_dice: 1.231752, mix_ce: 0.197797
[15:08:28.503] iteration 569: loss: 0.809010, mix_dice: 1.347827, mix_ce: 0.270193
[15:08:28.550] iteration 570: loss: 0.738153, mix_dice: 1.365992, mix_ce: 0.110313
[15:08:28.599] iteration 571: loss: 0.758547, mix_dice: 1.319432, mix_ce: 0.197663
[15:08:28.952] iteration 572: loss: 0.760682, mix_dice: 1.431558, mix_ce: 0.089807
[15:08:29.038] iteration 573: loss: 0.787839, mix_dice: 1.407661, mix_ce: 0.168017
[15:08:29.090] iteration 574: loss: 0.796552, mix_dice: 1.340318, mix_ce: 0.252787
[15:08:29.142] iteration 575: loss: 0.765899, mix_dice: 1.317513, mix_ce: 0.214285
[15:08:29.576] iteration 576: loss: 0.720443, mix_dice: 1.268931, mix_ce: 0.171954
[15:08:29.630] iteration 577: loss: 0.768205, mix_dice: 1.419810, mix_ce: 0.116599
[15:08:29.680] iteration 578: loss: 0.756590, mix_dice: 1.374362, mix_ce: 0.138818
[15:08:29.729] iteration 579: loss: 0.772852, mix_dice: 1.366958, mix_ce: 0.178747
[15:08:30.152] iteration 580: loss: 0.758543, mix_dice: 1.240245, mix_ce: 0.276842
[15:08:30.202] iteration 581: loss: 0.723006, mix_dice: 1.283921, mix_ce: 0.162092
[15:08:30.251] iteration 582: loss: 0.830581, mix_dice: 1.334839, mix_ce: 0.326324
[15:08:30.300] iteration 583: loss: 0.792534, mix_dice: 1.478013, mix_ce: 0.107056
[15:08:30.727] iteration 584: loss: 0.671547, mix_dice: 1.241407, mix_ce: 0.101687
[15:08:30.782] iteration 585: loss: 0.733867, mix_dice: 1.226736, mix_ce: 0.240998
[15:08:30.840] iteration 586: loss: 0.730919, mix_dice: 1.328173, mix_ce: 0.133665
[15:08:30.897] iteration 587: loss: 0.752203, mix_dice: 1.232220, mix_ce: 0.272186
[15:08:31.270] iteration 588: loss: 0.847650, mix_dice: 1.465452, mix_ce: 0.229848
[15:08:32.146] iteration 589: loss: 0.657896, mix_dice: 1.179700, mix_ce: 0.136092
[15:08:32.196] iteration 590: loss: 0.840123, mix_dice: 1.537203, mix_ce: 0.143044
[15:08:32.243] iteration 591: loss: 0.762617, mix_dice: 1.351451, mix_ce: 0.173784
[15:08:32.290] iteration 592: loss: 0.756443, mix_dice: 1.281199, mix_ce: 0.231688
[15:08:32.652] iteration 593: loss: 0.754481, mix_dice: 1.355944, mix_ce: 0.153019
[15:08:32.709] iteration 594: loss: 0.805989, mix_dice: 1.443684, mix_ce: 0.168294
[15:08:32.756] iteration 595: loss: 0.801557, mix_dice: 1.304172, mix_ce: 0.298943
[15:08:32.805] iteration 596: loss: 0.789117, mix_dice: 1.326995, mix_ce: 0.251239
[15:08:33.272] iteration 597: loss: 0.743611, mix_dice: 1.341866, mix_ce: 0.145356
[15:08:33.324] iteration 598: loss: 0.733731, mix_dice: 1.298585, mix_ce: 0.168877
[15:08:33.375] iteration 599: loss: 0.699411, mix_dice: 1.217061, mix_ce: 0.181761
[15:08:33.432] iteration 600: loss: 0.810122, mix_dice: 1.268443, mix_ce: 0.351801
[15:08:33.874] iteration 601: loss: 0.774348, mix_dice: 1.362119, mix_ce: 0.186577
[15:08:33.931] iteration 602: loss: 0.760516, mix_dice: 1.373279, mix_ce: 0.147752
[15:08:33.984] iteration 603: loss: 0.764047, mix_dice: 1.385596, mix_ce: 0.142499
[15:08:34.041] iteration 604: loss: 0.726567, mix_dice: 1.291468, mix_ce: 0.161666
[15:08:34.411] iteration 605: loss: 0.820017, mix_dice: 1.387200, mix_ce: 0.252835
[15:08:34.460] iteration 606: loss: 0.772416, mix_dice: 1.327762, mix_ce: 0.217071
[15:08:34.508] iteration 607: loss: 0.904523, mix_dice: 1.529400, mix_ce: 0.279646
[15:08:34.557] iteration 608: loss: 0.720262, mix_dice: 1.257778, mix_ce: 0.182747
[15:08:34.950] iteration 609: loss: 0.806265, mix_dice: 1.394561, mix_ce: 0.217968
[15:08:35.805] iteration 610: loss: 0.657869, mix_dice: 1.176400, mix_ce: 0.139338
[15:08:35.854] iteration 611: loss: 0.810091, mix_dice: 1.361324, mix_ce: 0.258857
[15:08:35.903] iteration 612: loss: 0.783350, mix_dice: 1.385781, mix_ce: 0.180919
[15:08:35.952] iteration 613: loss: 0.674397, mix_dice: 1.163333, mix_ce: 0.185460
[15:08:36.348] iteration 614: loss: 0.848112, mix_dice: 1.414834, mix_ce: 0.281390
[15:08:36.414] iteration 615: loss: 0.688194, mix_dice: 1.205248, mix_ce: 0.171139
[15:08:36.463] iteration 616: loss: 0.719277, mix_dice: 1.224861, mix_ce: 0.213694
[15:08:36.511] iteration 617: loss: 0.693796, mix_dice: 1.215891, mix_ce: 0.171700
[15:08:36.970] iteration 618: loss: 0.661544, mix_dice: 1.179355, mix_ce: 0.143733
[15:08:37.019] iteration 619: loss: 0.740700, mix_dice: 1.291533, mix_ce: 0.189867
[15:08:37.066] iteration 620: loss: 0.805305, mix_dice: 1.411449, mix_ce: 0.199162
[15:08:37.115] iteration 621: loss: 0.753718, mix_dice: 1.311257, mix_ce: 0.196179
[15:08:37.586] iteration 622: loss: 0.666728, mix_dice: 1.152324, mix_ce: 0.181133
[15:08:37.641] iteration 623: loss: 0.841889, mix_dice: 1.489328, mix_ce: 0.194450
[15:08:37.690] iteration 624: loss: 0.682826, mix_dice: 1.219638, mix_ce: 0.146015
[15:08:37.749] iteration 625: loss: 0.727095, mix_dice: 1.285721, mix_ce: 0.168470
[15:08:38.156] iteration 626: loss: 0.735267, mix_dice: 1.327885, mix_ce: 0.142650
[15:08:38.206] iteration 627: loss: 0.686137, mix_dice: 1.147971, mix_ce: 0.224302
[15:08:38.257] iteration 628: loss: 0.701602, mix_dice: 1.252304, mix_ce: 0.150900
[15:08:38.307] iteration 629: loss: 0.748901, mix_dice: 1.312575, mix_ce: 0.185228
[15:08:38.757] iteration 630: loss: 0.822416, mix_dice: 1.418644, mix_ce: 0.226188
[15:08:39.664] iteration 631: loss: 0.799637, mix_dice: 1.377430, mix_ce: 0.221843
[15:08:39.716] iteration 632: loss: 0.752476, mix_dice: 1.274136, mix_ce: 0.230815
[15:08:39.764] iteration 633: loss: 0.705676, mix_dice: 1.243474, mix_ce: 0.167878
[15:08:39.812] iteration 634: loss: 0.702662, mix_dice: 1.162453, mix_ce: 0.242872
[15:08:40.189] iteration 635: loss: 0.836819, mix_dice: 1.509179, mix_ce: 0.164459
[15:08:40.241] iteration 636: loss: 0.796954, mix_dice: 1.373358, mix_ce: 0.220550
[15:08:40.293] iteration 637: loss: 0.680847, mix_dice: 1.189101, mix_ce: 0.172593
[15:08:40.342] iteration 638: loss: 0.758299, mix_dice: 1.346843, mix_ce: 0.169755
[15:08:40.859] iteration 639: loss: 0.614489, mix_dice: 1.036191, mix_ce: 0.192787
[15:08:40.908] iteration 640: loss: 0.857100, mix_dice: 1.543775, mix_ce: 0.170424
[15:08:40.958] iteration 641: loss: 0.678458, mix_dice: 1.217139, mix_ce: 0.139777
[15:08:41.007] iteration 642: loss: 0.691911, mix_dice: 1.221879, mix_ce: 0.161943
[15:08:41.428] iteration 643: loss: 0.744387, mix_dice: 1.342204, mix_ce: 0.146571
[15:08:41.477] iteration 644: loss: 0.723702, mix_dice: 1.280216, mix_ce: 0.167188
[15:08:41.526] iteration 645: loss: 0.720120, mix_dice: 1.254459, mix_ce: 0.185781
[15:08:41.574] iteration 646: loss: 0.743643, mix_dice: 1.355319, mix_ce: 0.131968
[15:08:41.990] iteration 647: loss: 0.829544, mix_dice: 1.467779, mix_ce: 0.191309
[15:08:42.045] iteration 648: loss: 0.679719, mix_dice: 1.174268, mix_ce: 0.185170
[15:08:42.099] iteration 649: loss: 0.709248, mix_dice: 1.239587, mix_ce: 0.178909
[15:08:42.155] iteration 650: loss: 0.714955, mix_dice: 1.269216, mix_ce: 0.160694
[15:08:42.562] iteration 651: loss: 0.701078, mix_dice: 1.248726, mix_ce: 0.153430
[15:08:43.396] iteration 652: loss: 0.739284, mix_dice: 1.331369, mix_ce: 0.147199
[15:08:43.482] iteration 653: loss: 0.742999, mix_dice: 1.293091, mix_ce: 0.192908
[15:08:43.532] iteration 654: loss: 0.740171, mix_dice: 1.339271, mix_ce: 0.141071
[15:08:43.580] iteration 655: loss: 0.785367, mix_dice: 1.397339, mix_ce: 0.173396
[15:08:43.948] iteration 656: loss: 0.777603, mix_dice: 1.308000, mix_ce: 0.247206
[15:08:44.035] iteration 657: loss: 0.785497, mix_dice: 1.407172, mix_ce: 0.163823
[15:08:44.084] iteration 658: loss: 0.909244, mix_dice: 1.516212, mix_ce: 0.302276
[15:08:44.156] iteration 659: loss: 0.734760, mix_dice: 1.269346, mix_ce: 0.200174
[15:08:44.589] iteration 660: loss: 0.815318, mix_dice: 1.375285, mix_ce: 0.255351
[15:08:44.641] iteration 661: loss: 0.703051, mix_dice: 1.205514, mix_ce: 0.200587
[15:08:44.691] iteration 662: loss: 0.726020, mix_dice: 1.267300, mix_ce: 0.184741
[15:08:44.739] iteration 663: loss: 0.790081, mix_dice: 1.370174, mix_ce: 0.209987
[15:08:45.150] iteration 664: loss: 0.720967, mix_dice: 1.254646, mix_ce: 0.187288
[15:08:45.198] iteration 665: loss: 0.696918, mix_dice: 1.222735, mix_ce: 0.171102
[15:08:45.246] iteration 666: loss: 0.768691, mix_dice: 1.392605, mix_ce: 0.144776
[15:08:45.333] iteration 667: loss: 0.705771, mix_dice: 1.293724, mix_ce: 0.117817
[15:08:45.713] iteration 668: loss: 0.737754, mix_dice: 1.356040, mix_ce: 0.119468
[15:08:45.760] iteration 669: loss: 0.658774, mix_dice: 1.210320, mix_ce: 0.107228
[15:08:45.838] iteration 670: loss: 0.678565, mix_dice: 1.231540, mix_ce: 0.125590
[15:08:45.888] iteration 671: loss: 0.766553, mix_dice: 1.236134, mix_ce: 0.296972
[15:08:46.247] iteration 672: loss: 0.769432, mix_dice: 1.375999, mix_ce: 0.162864
[15:08:47.080] iteration 673: loss: 0.872386, mix_dice: 1.467785, mix_ce: 0.276987
[15:08:47.127] iteration 674: loss: 0.675748, mix_dice: 1.233967, mix_ce: 0.117529
[15:08:47.174] iteration 675: loss: 0.823028, mix_dice: 1.453876, mix_ce: 0.192180
[15:08:47.220] iteration 676: loss: 0.784359, mix_dice: 1.329390, mix_ce: 0.239327
[15:08:47.594] iteration 677: loss: 0.692616, mix_dice: 1.214372, mix_ce: 0.170860
[15:08:47.646] iteration 678: loss: 0.837007, mix_dice: 1.432071, mix_ce: 0.241943
[15:08:47.695] iteration 679: loss: 0.733894, mix_dice: 1.315906, mix_ce: 0.151883
[15:08:47.743] iteration 680: loss: 0.802146, mix_dice: 1.424400, mix_ce: 0.179893
[15:08:48.278] iteration 681: loss: 0.791698, mix_dice: 1.341106, mix_ce: 0.242290
[15:08:48.344] iteration 682: loss: 0.792200, mix_dice: 1.413208, mix_ce: 0.171192
[15:08:48.397] iteration 683: loss: 0.698508, mix_dice: 1.221414, mix_ce: 0.175602
[15:08:48.447] iteration 684: loss: 0.853570, mix_dice: 1.501000, mix_ce: 0.206140
[15:08:48.896] iteration 685: loss: 0.671549, mix_dice: 1.146937, mix_ce: 0.196161
[15:08:48.943] iteration 686: loss: 0.761677, mix_dice: 1.243154, mix_ce: 0.280200
[15:08:48.990] iteration 687: loss: 0.816934, mix_dice: 1.469339, mix_ce: 0.164529
[15:08:49.038] iteration 688: loss: 0.767768, mix_dice: 1.251537, mix_ce: 0.283999
[15:08:49.507] iteration 689: loss: 0.746235, mix_dice: 1.317481, mix_ce: 0.174989
[15:08:49.556] iteration 690: loss: 0.721482, mix_dice: 1.315525, mix_ce: 0.127438
[15:08:49.603] iteration 691: loss: 0.826831, mix_dice: 1.436671, mix_ce: 0.216990
[15:08:49.650] iteration 692: loss: 0.773451, mix_dice: 1.369422, mix_ce: 0.177480
[15:08:50.097] iteration 693: loss: 0.790537, mix_dice: 1.255724, mix_ce: 0.325349
[15:08:50.906] iteration 694: loss: 0.699039, mix_dice: 1.184420, mix_ce: 0.213659
[15:08:50.953] iteration 695: loss: 0.677628, mix_dice: 1.124527, mix_ce: 0.230730
[15:08:51.000] iteration 696: loss: 0.806082, mix_dice: 1.460484, mix_ce: 0.151680
[15:08:51.046] iteration 697: loss: 0.800102, mix_dice: 1.375067, mix_ce: 0.225136
[15:08:51.485] iteration 698: loss: 0.754708, mix_dice: 1.321525, mix_ce: 0.187892
[15:08:51.551] iteration 699: loss: 0.790673, mix_dice: 1.363865, mix_ce: 0.217480
[15:08:51.609] iteration 700: loss: 0.822675, mix_dice: 1.532781, mix_ce: 0.112569
[15:08:51.661] iteration 701: loss: 0.794987, mix_dice: 1.274538, mix_ce: 0.315436
[15:08:52.140] iteration 702: loss: 0.758688, mix_dice: 1.406150, mix_ce: 0.111227
[15:08:52.195] iteration 703: loss: 0.722140, mix_dice: 1.236507, mix_ce: 0.207774
[15:08:52.258] iteration 704: loss: 0.692189, mix_dice: 1.189924, mix_ce: 0.194454
[15:08:52.324] iteration 705: loss: 0.755943, mix_dice: 1.312460, mix_ce: 0.199427
[15:08:52.745] iteration 706: loss: 0.700249, mix_dice: 1.224089, mix_ce: 0.176408
[15:08:52.817] iteration 707: loss: 0.884212, mix_dice: 1.506651, mix_ce: 0.261774
[15:08:52.863] iteration 708: loss: 0.776910, mix_dice: 1.389096, mix_ce: 0.164725
[15:08:52.908] iteration 709: loss: 0.772575, mix_dice: 1.349887, mix_ce: 0.195262
[15:08:53.328] iteration 710: loss: 0.805180, mix_dice: 1.450297, mix_ce: 0.160063
[15:08:53.377] iteration 711: loss: 0.827012, mix_dice: 1.401641, mix_ce: 0.252384
[15:08:53.425] iteration 712: loss: 0.794240, mix_dice: 1.454597, mix_ce: 0.133883
[15:08:53.496] iteration 713: loss: 0.688677, mix_dice: 1.214712, mix_ce: 0.162643
[15:08:53.920] iteration 714: loss: 0.757893, mix_dice: 1.376507, mix_ce: 0.139278
[15:08:54.798] iteration 715: loss: 0.657784, mix_dice: 1.204051, mix_ce: 0.111517
[15:08:54.907] iteration 716: loss: 0.748048, mix_dice: 1.306243, mix_ce: 0.189852
[15:08:54.977] iteration 717: loss: 0.684006, mix_dice: 1.164056, mix_ce: 0.203955
[15:08:55.062] iteration 718: loss: 0.654172, mix_dice: 1.183651, mix_ce: 0.124693
[15:08:55.265] iteration 719: loss: 0.760935, mix_dice: 1.326417, mix_ce: 0.195453
[15:08:55.320] iteration 720: loss: 0.667319, mix_dice: 1.158709, mix_ce: 0.175929
[15:08:55.374] iteration 721: loss: 0.708434, mix_dice: 1.307716, mix_ce: 0.109152
[15:08:55.422] iteration 722: loss: 0.692025, mix_dice: 1.250007, mix_ce: 0.134044
[15:08:55.904] iteration 723: loss: 0.708501, mix_dice: 1.184652, mix_ce: 0.232350
[15:08:55.962] iteration 724: loss: 0.731104, mix_dice: 1.299404, mix_ce: 0.162805
[15:08:56.014] iteration 725: loss: 0.827221, mix_dice: 1.383914, mix_ce: 0.270529
[15:08:56.073] iteration 726: loss: 0.763589, mix_dice: 1.387630, mix_ce: 0.139548
[15:08:56.472] iteration 727: loss: 0.803927, mix_dice: 1.435190, mix_ce: 0.172664
[15:08:56.521] iteration 728: loss: 0.766147, mix_dice: 1.267574, mix_ce: 0.264720
[15:08:56.567] iteration 729: loss: 0.866860, mix_dice: 1.576095, mix_ce: 0.157625
[15:08:56.612] iteration 730: loss: 0.761848, mix_dice: 1.371298, mix_ce: 0.152398
[15:08:57.021] iteration 731: loss: 0.728338, mix_dice: 1.328943, mix_ce: 0.127733
[15:08:57.066] iteration 732: loss: 0.683633, mix_dice: 1.151108, mix_ce: 0.216158
[15:08:57.113] iteration 733: loss: 0.641119, mix_dice: 1.092763, mix_ce: 0.189475
[15:08:57.160] iteration 734: loss: 0.722775, mix_dice: 1.296333, mix_ce: 0.149216
[15:08:57.571] iteration 735: loss: 0.650547, mix_dice: 1.140533, mix_ce: 0.160561
[15:08:58.428] iteration 736: loss: 0.652282, mix_dice: 1.115560, mix_ce: 0.189005
[15:08:58.477] iteration 737: loss: 0.744629, mix_dice: 1.401572, mix_ce: 0.087686
[15:08:58.537] iteration 738: loss: 0.598571, mix_dice: 1.002594, mix_ce: 0.194547
[15:08:58.585] iteration 739: loss: 0.779535, mix_dice: 1.366346, mix_ce: 0.192724
[15:08:58.960] iteration 740: loss: 0.784375, mix_dice: 1.398408, mix_ce: 0.170343
[15:08:59.011] iteration 741: loss: 0.863954, mix_dice: 1.547085, mix_ce: 0.180823
[15:08:59.061] iteration 742: loss: 0.649042, mix_dice: 1.175581, mix_ce: 0.122504
[15:08:59.111] iteration 743: loss: 0.795505, mix_dice: 1.310862, mix_ce: 0.280148
[15:08:59.628] iteration 744: loss: 0.834075, mix_dice: 1.504716, mix_ce: 0.163435
[15:08:59.675] iteration 745: loss: 0.762789, mix_dice: 1.319880, mix_ce: 0.205698
[15:08:59.724] iteration 746: loss: 0.664332, mix_dice: 1.136487, mix_ce: 0.192177
[15:08:59.772] iteration 747: loss: 0.659475, mix_dice: 1.183283, mix_ce: 0.135667
[15:09:00.225] iteration 748: loss: 0.756141, mix_dice: 1.371024, mix_ce: 0.141258
[15:09:00.273] iteration 749: loss: 0.860852, mix_dice: 1.626374, mix_ce: 0.095330
[15:09:00.323] iteration 750: loss: 0.641132, mix_dice: 1.051411, mix_ce: 0.230853
[15:09:00.371] iteration 751: loss: 0.701350, mix_dice: 1.240012, mix_ce: 0.162688
[15:09:00.808] iteration 752: loss: 0.710234, mix_dice: 1.249739, mix_ce: 0.170730
[15:09:00.858] iteration 753: loss: 0.653096, mix_dice: 1.147055, mix_ce: 0.159137
[15:09:00.906] iteration 754: loss: 0.724719, mix_dice: 1.212607, mix_ce: 0.236832
[15:09:00.956] iteration 755: loss: 0.754949, mix_dice: 1.378786, mix_ce: 0.131112
[15:09:01.369] iteration 756: loss: 0.677834, mix_dice: 1.159893, mix_ce: 0.195775
[15:09:02.202] iteration 757: loss: 0.675441, mix_dice: 1.235975, mix_ce: 0.114908
[15:09:02.252] iteration 758: loss: 0.780169, mix_dice: 1.335952, mix_ce: 0.224387
[15:09:02.301] iteration 759: loss: 0.754757, mix_dice: 1.289078, mix_ce: 0.220436
[15:09:02.349] iteration 760: loss: 0.724236, mix_dice: 1.279272, mix_ce: 0.169201
[15:09:02.755] iteration 761: loss: 0.731802, mix_dice: 1.299372, mix_ce: 0.164232
[15:09:02.807] iteration 762: loss: 0.673160, mix_dice: 1.191390, mix_ce: 0.154929
[15:09:02.859] iteration 763: loss: 0.665771, mix_dice: 1.112307, mix_ce: 0.219236
[15:09:02.906] iteration 764: loss: 0.680306, mix_dice: 1.178087, mix_ce: 0.182526
[15:09:03.375] iteration 765: loss: 0.706722, mix_dice: 1.224492, mix_ce: 0.188951
[15:09:03.423] iteration 766: loss: 0.762352, mix_dice: 1.332918, mix_ce: 0.191786
[15:09:03.471] iteration 767: loss: 0.669362, mix_dice: 1.127972, mix_ce: 0.210751
[15:09:03.520] iteration 768: loss: 0.653988, mix_dice: 1.145415, mix_ce: 0.162561
[15:09:03.947] iteration 769: loss: 0.770330, mix_dice: 1.420111, mix_ce: 0.120548
[15:09:03.998] iteration 770: loss: 0.686995, mix_dice: 1.247175, mix_ce: 0.126814
[15:09:04.047] iteration 771: loss: 0.722604, mix_dice: 1.275197, mix_ce: 0.170011
[15:09:04.094] iteration 772: loss: 0.707796, mix_dice: 1.225823, mix_ce: 0.189769
[15:09:04.513] iteration 773: loss: 0.700073, mix_dice: 1.130619, mix_ce: 0.269527
[15:09:04.560] iteration 774: loss: 0.651551, mix_dice: 1.176747, mix_ce: 0.126355
[15:09:04.608] iteration 775: loss: 0.672690, mix_dice: 1.087427, mix_ce: 0.257953
[15:09:04.658] iteration 776: loss: 0.818390, mix_dice: 1.469354, mix_ce: 0.167426
[15:09:05.074] iteration 777: loss: 0.822787, mix_dice: 1.440032, mix_ce: 0.205542
[15:09:05.966] iteration 778: loss: 0.753067, mix_dice: 1.324024, mix_ce: 0.182110
[15:09:06.021] iteration 779: loss: 0.756484, mix_dice: 1.371932, mix_ce: 0.141036
[15:09:06.071] iteration 780: loss: 0.775779, mix_dice: 1.335012, mix_ce: 0.216546
[15:09:06.119] iteration 781: loss: 0.709420, mix_dice: 1.217924, mix_ce: 0.200916
[15:09:06.470] iteration 782: loss: 0.680682, mix_dice: 1.178385, mix_ce: 0.182980
[15:09:06.520] iteration 783: loss: 0.711835, mix_dice: 1.239494, mix_ce: 0.184177
[15:09:06.569] iteration 784: loss: 0.865249, mix_dice: 1.509303, mix_ce: 0.221195
[15:09:06.618] iteration 785: loss: 0.631436, mix_dice: 1.130007, mix_ce: 0.132866
[15:09:07.102] iteration 786: loss: 0.799621, mix_dice: 1.391158, mix_ce: 0.208083
[15:09:07.150] iteration 787: loss: 0.752628, mix_dice: 1.221005, mix_ce: 0.284251
[15:09:07.210] iteration 788: loss: 0.689744, mix_dice: 1.187393, mix_ce: 0.192095
[15:09:07.267] iteration 789: loss: 0.689225, mix_dice: 1.161779, mix_ce: 0.216670
[15:09:07.731] iteration 790: loss: 0.742392, mix_dice: 1.382263, mix_ce: 0.102522
[15:09:07.794] iteration 791: loss: 0.734066, mix_dice: 1.341369, mix_ce: 0.126764
[15:09:07.849] iteration 792: loss: 0.656270, mix_dice: 1.134062, mix_ce: 0.178478
[15:09:07.919] iteration 793: loss: 0.593295, mix_dice: 1.055752, mix_ce: 0.130838
[15:09:08.321] iteration 794: loss: 0.697166, mix_dice: 1.207553, mix_ce: 0.186778
[15:09:08.380] iteration 795: loss: 0.755441, mix_dice: 1.246776, mix_ce: 0.264107
[15:09:08.436] iteration 796: loss: 0.718692, mix_dice: 1.294880, mix_ce: 0.142504
[15:09:08.495] iteration 797: loss: 0.709212, mix_dice: 1.307748, mix_ce: 0.110677
[15:09:08.848] iteration 798: loss: 0.775901, mix_dice: 1.336523, mix_ce: 0.215279
[15:09:09.670] iteration 799: loss: 0.628215, mix_dice: 1.042120, mix_ce: 0.214310
[15:09:09.722] iteration 800: loss: 0.802816, mix_dice: 1.367786, mix_ce: 0.237846
[15:09:09.784] iteration 801: loss: 0.738799, mix_dice: 1.288245, mix_ce: 0.189353
[15:09:09.860] iteration 802: loss: 0.622248, mix_dice: 1.108465, mix_ce: 0.136032
[15:09:10.186] iteration 803: loss: 0.758976, mix_dice: 1.306780, mix_ce: 0.211172
[15:09:10.257] iteration 804: loss: 0.700652, mix_dice: 1.255980, mix_ce: 0.145325
[15:09:10.318] iteration 805: loss: 0.737337, mix_dice: 1.257205, mix_ce: 0.217469
[15:09:10.380] iteration 806: loss: 0.685651, mix_dice: 1.152171, mix_ce: 0.219130
[15:09:10.818] iteration 807: loss: 0.673703, mix_dice: 1.191078, mix_ce: 0.156327
[15:09:10.866] iteration 808: loss: 0.809535, mix_dice: 1.307944, mix_ce: 0.311127
[15:09:10.916] iteration 809: loss: 0.658138, mix_dice: 1.150631, mix_ce: 0.165644
[15:09:10.970] iteration 810: loss: 0.622679, mix_dice: 0.967385, mix_ce: 0.277973
[15:09:11.422] iteration 811: loss: 0.613821, mix_dice: 1.034893, mix_ce: 0.192749
[15:09:11.469] iteration 812: loss: 0.699813, mix_dice: 1.184740, mix_ce: 0.214886
[15:09:11.519] iteration 813: loss: 0.745118, mix_dice: 1.325152, mix_ce: 0.165085
[15:09:11.582] iteration 814: loss: 0.661980, mix_dice: 1.205505, mix_ce: 0.118455
[15:09:12.041] iteration 815: loss: 0.657359, mix_dice: 1.175761, mix_ce: 0.138957
[15:09:12.094] iteration 816: loss: 0.658358, mix_dice: 1.203211, mix_ce: 0.113505
[15:09:12.144] iteration 817: loss: 0.655814, mix_dice: 1.188156, mix_ce: 0.123473
[15:09:12.205] iteration 818: loss: 0.729584, mix_dice: 1.334474, mix_ce: 0.124694
[15:09:12.626] iteration 819: loss: 0.821196, mix_dice: 1.545594, mix_ce: 0.096798
[15:09:13.511] iteration 820: loss: 0.774676, mix_dice: 1.340781, mix_ce: 0.208570
[15:09:13.567] iteration 821: loss: 0.656218, mix_dice: 1.080369, mix_ce: 0.232066
[15:09:13.623] iteration 822: loss: 0.599584, mix_dice: 1.040592, mix_ce: 0.158576
[15:09:13.682] iteration 823: loss: 0.687401, mix_dice: 1.234228, mix_ce: 0.140573
[15:09:14.012] iteration 824: loss: 0.716352, mix_dice: 1.313748, mix_ce: 0.118957
[15:09:14.089] iteration 825: loss: 0.703611, mix_dice: 1.285489, mix_ce: 0.121733
[15:09:14.145] iteration 826: loss: 0.819672, mix_dice: 1.506783, mix_ce: 0.132562
[15:09:14.196] iteration 827: loss: 0.643977, mix_dice: 1.163416, mix_ce: 0.124538
[15:09:14.645] iteration 828: loss: 0.720888, mix_dice: 1.361580, mix_ce: 0.080197
[15:09:14.744] iteration 829: loss: 0.658858, mix_dice: 1.114863, mix_ce: 0.202853
[15:09:14.866] iteration 830: loss: 0.656428, mix_dice: 1.170001, mix_ce: 0.142855
[15:09:14.976] iteration 831: loss: 0.740354, mix_dice: 1.364646, mix_ce: 0.116061
[15:09:15.300] iteration 832: loss: 0.658859, mix_dice: 1.171343, mix_ce: 0.146376
[15:09:15.366] iteration 833: loss: 0.666407, mix_dice: 1.208044, mix_ce: 0.124771
[15:09:15.463] iteration 834: loss: 0.753854, mix_dice: 1.285612, mix_ce: 0.222096
[15:09:15.545] iteration 835: loss: 0.560551, mix_dice: 0.914861, mix_ce: 0.206241
[15:09:15.895] iteration 836: loss: 0.717510, mix_dice: 1.304049, mix_ce: 0.130971
[15:09:16.004] iteration 837: loss: 0.619639, mix_dice: 1.060139, mix_ce: 0.179139
[15:09:16.070] iteration 838: loss: 0.674516, mix_dice: 1.130026, mix_ce: 0.219006
[15:09:16.138] iteration 839: loss: 0.683481, mix_dice: 1.153715, mix_ce: 0.213247
[15:09:16.500] iteration 840: loss: 0.699521, mix_dice: 1.183603, mix_ce: 0.215439
[15:09:17.474] iteration 841: loss: 0.649168, mix_dice: 1.202869, mix_ce: 0.095468
[15:09:17.575] iteration 842: loss: 0.790860, mix_dice: 1.230641, mix_ce: 0.351079
[15:09:17.664] iteration 843: loss: 0.649539, mix_dice: 1.135146, mix_ce: 0.163932
[15:09:17.748] iteration 844: loss: 0.712621, mix_dice: 1.190135, mix_ce: 0.235107
[15:09:17.993] iteration 845: loss: 0.857918, mix_dice: 1.494113, mix_ce: 0.221722
[15:09:18.095] iteration 846: loss: 0.690349, mix_dice: 1.252725, mix_ce: 0.127972
[15:09:18.186] iteration 847: loss: 0.675435, mix_dice: 1.262413, mix_ce: 0.088457
[15:09:18.289] iteration 848: loss: 0.658410, mix_dice: 1.135462, mix_ce: 0.181359
[15:09:18.596] iteration 849: loss: 0.583255, mix_dice: 1.035683, mix_ce: 0.130827
[15:09:18.654] iteration 850: loss: 0.668797, mix_dice: 1.196724, mix_ce: 0.140870
[15:09:18.723] iteration 851: loss: 0.660224, mix_dice: 1.143791, mix_ce: 0.176658
[15:09:18.791] iteration 852: loss: 0.705227, mix_dice: 1.220642, mix_ce: 0.189811
[15:09:19.232] iteration 853: loss: 0.738274, mix_dice: 1.300767, mix_ce: 0.175782
[15:09:19.301] iteration 854: loss: 0.699662, mix_dice: 1.237071, mix_ce: 0.162253
[15:09:19.370] iteration 855: loss: 0.564643, mix_dice: 0.982856, mix_ce: 0.146431
[15:09:19.436] iteration 856: loss: 0.682384, mix_dice: 1.187081, mix_ce: 0.177687
[15:09:19.837] iteration 857: loss: 0.622364, mix_dice: 1.150941, mix_ce: 0.093787
[15:09:19.902] iteration 858: loss: 0.555757, mix_dice: 0.883696, mix_ce: 0.227819
[15:09:19.971] iteration 859: loss: 0.705747, mix_dice: 1.301395, mix_ce: 0.110099
[15:09:20.068] iteration 860: loss: 0.824262, mix_dice: 1.493415, mix_ce: 0.155109
[15:09:20.401] iteration 861: loss: 0.620826, mix_dice: 1.096050, mix_ce: 0.145602
[15:09:21.288] iteration 862: loss: 0.681323, mix_dice: 1.245425, mix_ce: 0.117220
[15:09:21.340] iteration 863: loss: 0.580724, mix_dice: 0.978354, mix_ce: 0.183094
[15:09:21.395] iteration 864: loss: 0.559898, mix_dice: 0.989129, mix_ce: 0.130668
[15:09:21.448] iteration 865: loss: 0.769340, mix_dice: 1.331989, mix_ce: 0.206691
[15:09:21.866] iteration 866: loss: 0.666063, mix_dice: 1.159844, mix_ce: 0.172282
[15:09:21.930] iteration 867: loss: 0.679124, mix_dice: 1.185941, mix_ce: 0.172307
[15:09:21.983] iteration 868: loss: 0.600377, mix_dice: 0.993231, mix_ce: 0.207523
[15:09:22.040] iteration 869: loss: 0.687744, mix_dice: 1.222172, mix_ce: 0.153315
[15:09:22.499] iteration 870: loss: 0.606479, mix_dice: 1.047496, mix_ce: 0.165463
[15:09:22.547] iteration 871: loss: 0.756105, mix_dice: 1.406955, mix_ce: 0.105254
[15:09:22.599] iteration 872: loss: 0.686433, mix_dice: 1.179372, mix_ce: 0.193495
[15:09:22.646] iteration 873: loss: 0.684150, mix_dice: 1.273832, mix_ce: 0.094468
[15:09:23.106] iteration 874: loss: 0.621670, mix_dice: 1.052990, mix_ce: 0.190350
[15:09:23.161] iteration 875: loss: 0.725300, mix_dice: 1.295511, mix_ce: 0.155089
[15:09:23.212] iteration 876: loss: 0.695094, mix_dice: 1.263467, mix_ce: 0.126721
[15:09:23.283] iteration 877: loss: 0.628743, mix_dice: 1.153054, mix_ce: 0.104432
[15:09:23.735] iteration 878: loss: 0.612645, mix_dice: 1.106525, mix_ce: 0.118765
[15:09:23.793] iteration 879: loss: 0.706927, mix_dice: 1.251325, mix_ce: 0.162529
[15:09:23.858] iteration 880: loss: 0.835496, mix_dice: 1.427930, mix_ce: 0.243061
[15:09:23.928] iteration 881: loss: 0.727377, mix_dice: 1.320805, mix_ce: 0.133948
[15:09:24.284] iteration 882: loss: 0.672986, mix_dice: 1.157645, mix_ce: 0.188327
[15:09:25.101] iteration 883: loss: 0.696846, mix_dice: 1.227880, mix_ce: 0.165812
[15:09:25.159] iteration 884: loss: 0.678154, mix_dice: 1.180905, mix_ce: 0.175404
[15:09:25.208] iteration 885: loss: 0.647874, mix_dice: 1.137069, mix_ce: 0.158679
[15:09:25.260] iteration 886: loss: 0.582784, mix_dice: 1.021144, mix_ce: 0.144423
[15:09:25.622] iteration 887: loss: 0.589366, mix_dice: 1.011276, mix_ce: 0.167456
[15:09:25.714] iteration 888: loss: 0.800794, mix_dice: 1.440130, mix_ce: 0.161457
[15:09:25.765] iteration 889: loss: 0.731195, mix_dice: 1.221904, mix_ce: 0.240486
[15:09:25.814] iteration 890: loss: 0.705520, mix_dice: 1.242132, mix_ce: 0.168908
[15:09:26.291] iteration 891: loss: 0.669623, mix_dice: 1.129616, mix_ce: 0.209630
[15:09:26.339] iteration 892: loss: 0.639216, mix_dice: 1.058709, mix_ce: 0.219723
[15:09:26.388] iteration 893: loss: 0.663200, mix_dice: 1.192774, mix_ce: 0.133626
[15:09:26.435] iteration 894: loss: 0.672809, mix_dice: 1.158938, mix_ce: 0.186680
[15:09:26.882] iteration 895: loss: 0.736016, mix_dice: 1.297741, mix_ce: 0.174290
[15:09:26.937] iteration 896: loss: 0.605825, mix_dice: 1.084009, mix_ce: 0.127642
[15:09:27.004] iteration 897: loss: 0.809373, mix_dice: 1.490063, mix_ce: 0.128683
[15:09:27.064] iteration 898: loss: 0.630493, mix_dice: 1.141531, mix_ce: 0.119455
[15:09:27.457] iteration 899: loss: 0.611473, mix_dice: 1.110680, mix_ce: 0.112266
[15:09:27.509] iteration 900: loss: 0.774311, mix_dice: 1.479743, mix_ce: 0.068880
[15:09:27.560] iteration 901: loss: 0.650327, mix_dice: 1.137420, mix_ce: 0.163234
[15:09:27.611] iteration 902: loss: 0.606825, mix_dice: 1.080350, mix_ce: 0.133300
[15:09:28.020] iteration 903: loss: 0.683261, mix_dice: 1.238043, mix_ce: 0.128479
[15:09:28.888] iteration 904: loss: 0.749734, mix_dice: 1.396827, mix_ce: 0.102640
[15:09:28.947] iteration 905: loss: 0.658020, mix_dice: 1.056508, mix_ce: 0.259533
[15:09:28.995] iteration 906: loss: 0.560526, mix_dice: 0.970356, mix_ce: 0.150696
[15:09:29.044] iteration 907: loss: 0.682992, mix_dice: 1.176100, mix_ce: 0.189883
[15:09:29.392] iteration 908: loss: 0.669509, mix_dice: 1.156405, mix_ce: 0.182613
[15:09:29.519] iteration 909: loss: 0.589038, mix_dice: 1.056327, mix_ce: 0.121749
[15:09:29.570] iteration 910: loss: 0.680415, mix_dice: 1.237782, mix_ce: 0.123047
[15:09:29.619] iteration 911: loss: 0.575899, mix_dice: 1.042970, mix_ce: 0.108828
[15:09:30.062] iteration 912: loss: 0.580512, mix_dice: 1.067655, mix_ce: 0.093368
[15:09:30.113] iteration 913: loss: 0.672524, mix_dice: 1.155424, mix_ce: 0.189624
[15:09:30.161] iteration 914: loss: 0.694483, mix_dice: 1.148877, mix_ce: 0.240089
[15:09:30.209] iteration 915: loss: 0.714528, mix_dice: 1.276817, mix_ce: 0.152238
[15:09:30.675] iteration 916: loss: 0.678103, mix_dice: 1.170882, mix_ce: 0.185325
[15:09:30.729] iteration 917: loss: 0.654767, mix_dice: 1.178168, mix_ce: 0.131367
[15:09:30.780] iteration 918: loss: 0.612144, mix_dice: 1.093144, mix_ce: 0.131145
[15:09:30.833] iteration 919: loss: 0.681487, mix_dice: 1.231611, mix_ce: 0.131362
[15:09:31.241] iteration 920: loss: 0.689161, mix_dice: 1.254414, mix_ce: 0.123908
[15:09:31.295] iteration 921: loss: 0.459236, mix_dice: 0.793076, mix_ce: 0.125396
[15:09:31.346] iteration 922: loss: 0.625418, mix_dice: 1.057702, mix_ce: 0.193134
[15:09:31.395] iteration 923: loss: 0.597716, mix_dice: 1.068406, mix_ce: 0.127025
[15:09:31.802] iteration 924: loss: 0.574198, mix_dice: 0.946406, mix_ce: 0.201990
[15:09:32.680] iteration 925: loss: 0.686019, mix_dice: 1.184967, mix_ce: 0.187071
[15:09:32.740] iteration 926: loss: 0.627823, mix_dice: 1.087586, mix_ce: 0.168059
[15:09:32.787] iteration 927: loss: 0.667740, mix_dice: 1.075798, mix_ce: 0.259681
[15:09:32.841] iteration 928: loss: 0.653065, mix_dice: 1.092113, mix_ce: 0.214018
[15:09:33.188] iteration 929: loss: 0.634726, mix_dice: 1.175093, mix_ce: 0.094358
[15:09:33.240] iteration 930: loss: 0.704633, mix_dice: 1.197078, mix_ce: 0.212188
[15:09:33.296] iteration 931: loss: 0.637287, mix_dice: 1.166690, mix_ce: 0.107884
[15:09:33.361] iteration 932: loss: 0.592725, mix_dice: 1.005959, mix_ce: 0.179491
[15:09:33.851] iteration 933: loss: 0.557491, mix_dice: 0.965519, mix_ce: 0.149462
[15:09:33.903] iteration 934: loss: 0.635977, mix_dice: 1.138000, mix_ce: 0.133954
[15:09:33.954] iteration 935: loss: 0.558405, mix_dice: 0.973129, mix_ce: 0.143682
[15:09:34.001] iteration 936: loss: 0.759117, mix_dice: 1.396426, mix_ce: 0.121808
[15:09:34.406] iteration 937: loss: 0.572953, mix_dice: 1.031450, mix_ce: 0.114456
[15:09:34.456] iteration 938: loss: 0.703496, mix_dice: 1.331433, mix_ce: 0.075559
[15:09:34.510] iteration 939: loss: 0.579455, mix_dice: 1.032521, mix_ce: 0.126389
[15:09:34.569] iteration 940: loss: 0.679932, mix_dice: 1.141900, mix_ce: 0.217963
[15:09:34.999] iteration 941: loss: 0.645949, mix_dice: 1.214344, mix_ce: 0.077555
[15:09:35.052] iteration 942: loss: 0.702267, mix_dice: 1.326222, mix_ce: 0.078312
[15:09:35.106] iteration 943: loss: 0.670671, mix_dice: 1.177861, mix_ce: 0.163481
[15:09:35.156] iteration 944: loss: 0.693077, mix_dice: 1.144158, mix_ce: 0.241995
[15:09:35.571] iteration 945: loss: 0.667133, mix_dice: 1.239958, mix_ce: 0.094308
[15:09:36.446] iteration 946: loss: 0.575832, mix_dice: 0.908983, mix_ce: 0.242680
[15:09:36.505] iteration 947: loss: 0.554526, mix_dice: 0.991245, mix_ce: 0.117807
[15:09:36.552] iteration 948: loss: 0.717568, mix_dice: 1.176521, mix_ce: 0.258616
[15:09:36.600] iteration 949: loss: 0.578442, mix_dice: 1.003816, mix_ce: 0.153069
[15:09:36.940] iteration 950: loss: 0.733538, mix_dice: 1.331416, mix_ce: 0.135661
[15:09:37.000] iteration 951: loss: 0.683394, mix_dice: 1.150553, mix_ce: 0.216235
[15:09:37.051] iteration 952: loss: 0.609681, mix_dice: 1.047951, mix_ce: 0.171411
[15:09:37.098] iteration 953: loss: 0.664091, mix_dice: 1.184298, mix_ce: 0.143884
[15:09:37.555] iteration 954: loss: 0.707675, mix_dice: 1.277937, mix_ce: 0.137412
[15:09:37.608] iteration 955: loss: 0.612457, mix_dice: 1.057936, mix_ce: 0.166978
[15:09:37.673] iteration 956: loss: 0.721259, mix_dice: 1.263144, mix_ce: 0.179374
[15:09:37.722] iteration 957: loss: 0.682723, mix_dice: 1.258617, mix_ce: 0.106829
[15:09:38.154] iteration 958: loss: 0.703136, mix_dice: 1.219565, mix_ce: 0.186706
[15:09:38.203] iteration 959: loss: 0.543579, mix_dice: 0.920935, mix_ce: 0.166223
[15:09:38.252] iteration 960: loss: 0.662773, mix_dice: 1.201996, mix_ce: 0.123549
[15:09:38.300] iteration 961: loss: 0.554415, mix_dice: 0.985645, mix_ce: 0.123185
[15:09:38.746] iteration 962: loss: 0.570030, mix_dice: 1.034040, mix_ce: 0.106021
[15:09:38.804] iteration 963: loss: 0.649407, mix_dice: 1.109091, mix_ce: 0.189723
[15:09:38.856] iteration 964: loss: 0.726077, mix_dice: 1.130967, mix_ce: 0.321188
[15:09:38.916] iteration 965: loss: 0.718282, mix_dice: 1.302545, mix_ce: 0.134018
[15:09:39.292] iteration 966: loss: 0.660612, mix_dice: 1.114201, mix_ce: 0.207022
[15:09:40.104] iteration 967: loss: 0.570603, mix_dice: 1.010156, mix_ce: 0.131049
[15:09:40.153] iteration 968: loss: 0.647320, mix_dice: 1.137980, mix_ce: 0.156660
[15:09:40.201] iteration 969: loss: 0.650989, mix_dice: 1.124489, mix_ce: 0.177489
[15:09:40.248] iteration 970: loss: 0.624847, mix_dice: 1.092487, mix_ce: 0.157207
[15:09:40.629] iteration 971: loss: 0.599199, mix_dice: 1.041528, mix_ce: 0.156871
[15:09:40.688] iteration 972: loss: 0.720973, mix_dice: 1.288385, mix_ce: 0.153561
[15:09:40.752] iteration 973: loss: 0.704731, mix_dice: 1.244717, mix_ce: 0.164745
[15:09:40.815] iteration 974: loss: 0.570084, mix_dice: 1.008054, mix_ce: 0.132114
[15:09:41.291] iteration 975: loss: 0.671821, mix_dice: 1.196475, mix_ce: 0.147166
[15:09:41.342] iteration 976: loss: 0.593893, mix_dice: 1.024550, mix_ce: 0.163235
[15:09:41.401] iteration 977: loss: 0.763556, mix_dice: 1.408515, mix_ce: 0.118597
[15:09:41.464] iteration 978: loss: 0.754838, mix_dice: 1.413337, mix_ce: 0.096339
[15:09:41.855] iteration 979: loss: 0.522529, mix_dice: 0.887167, mix_ce: 0.157892
[15:09:41.907] iteration 980: loss: 0.604248, mix_dice: 1.002790, mix_ce: 0.205707
[15:09:41.964] iteration 981: loss: 0.613403, mix_dice: 1.080940, mix_ce: 0.145867
[15:09:42.022] iteration 982: loss: 0.680299, mix_dice: 1.249630, mix_ce: 0.110968
[15:09:42.449] iteration 983: loss: 0.569251, mix_dice: 1.000158, mix_ce: 0.138345
[15:09:42.529] iteration 984: loss: 0.694227, mix_dice: 1.293936, mix_ce: 0.094519
[15:09:42.606] iteration 985: loss: 0.748399, mix_dice: 1.317527, mix_ce: 0.179270
[15:09:42.656] iteration 986: loss: 0.592022, mix_dice: 0.968844, mix_ce: 0.215200
[15:09:43.006] iteration 987: loss: 0.685461, mix_dice: 1.246312, mix_ce: 0.124610
[15:09:43.917] iteration 988: loss: 0.638170, mix_dice: 1.153116, mix_ce: 0.123223
[15:09:43.989] iteration 989: loss: 0.592365, mix_dice: 1.016821, mix_ce: 0.167908
[15:09:44.049] iteration 990: loss: 0.584492, mix_dice: 1.042543, mix_ce: 0.126440
[15:09:44.113] iteration 991: loss: 0.672843, mix_dice: 1.140569, mix_ce: 0.205117
[15:09:44.389] iteration 992: loss: 0.580375, mix_dice: 0.939674, mix_ce: 0.221075
[15:09:44.495] iteration 993: loss: 0.548053, mix_dice: 0.933541, mix_ce: 0.162565
[15:09:44.543] iteration 994: loss: 0.647058, mix_dice: 1.132149, mix_ce: 0.161967
[15:09:44.591] iteration 995: loss: 0.648119, mix_dice: 1.152640, mix_ce: 0.143597
[15:09:45.008] iteration 996: loss: 0.558336, mix_dice: 1.017632, mix_ce: 0.099039
[15:09:45.144] iteration 997: loss: 0.695318, mix_dice: 1.130970, mix_ce: 0.259666
[15:09:45.191] iteration 998: loss: 0.889341, mix_dice: 1.633735, mix_ce: 0.144947
[15:09:45.245] iteration 999: loss: 0.596187, mix_dice: 1.075102, mix_ce: 0.117272
[15:09:45.606] iteration 1000: loss: 0.510040, mix_dice: 0.886774, mix_ce: 0.133305
[15:09:45.698] iteration 1001: loss: 0.684615, mix_dice: 1.167400, mix_ce: 0.201830
[15:09:45.746] iteration 1002: loss: 0.471287, mix_dice: 0.822763, mix_ce: 0.119810
[15:09:45.794] iteration 1003: loss: 0.716098, mix_dice: 1.279167, mix_ce: 0.153028
[15:09:46.186] iteration 1004: loss: 0.664033, mix_dice: 1.228880, mix_ce: 0.099187
[15:09:46.303] iteration 1005: loss: 0.606708, mix_dice: 1.076983, mix_ce: 0.136432
[15:09:46.350] iteration 1006: loss: 0.693662, mix_dice: 1.296783, mix_ce: 0.090542
[15:09:46.396] iteration 1007: loss: 0.698537, mix_dice: 1.174419, mix_ce: 0.222656
[15:09:46.749] iteration 1008: loss: 0.694791, mix_dice: 1.200986, mix_ce: 0.188595
[15:09:47.557] iteration 1009: loss: 0.577665, mix_dice: 0.963120, mix_ce: 0.192211
[15:09:47.605] iteration 1010: loss: 0.637841, mix_dice: 1.184391, mix_ce: 0.091290
[15:09:47.660] iteration 1011: loss: 0.727168, mix_dice: 1.306811, mix_ce: 0.147525
[15:09:47.707] iteration 1012: loss: 0.576503, mix_dice: 0.927199, mix_ce: 0.225807
[15:09:48.070] iteration 1013: loss: 0.658412, mix_dice: 1.148723, mix_ce: 0.168102
[15:09:48.118] iteration 1014: loss: 0.597049, mix_dice: 1.027602, mix_ce: 0.166496
[15:09:48.163] iteration 1015: loss: 0.631689, mix_dice: 1.130315, mix_ce: 0.133062
[15:09:48.209] iteration 1016: loss: 0.792733, mix_dice: 1.481486, mix_ce: 0.103980
[15:09:48.741] iteration 1017: loss: 0.719351, mix_dice: 1.156006, mix_ce: 0.282696
[15:09:48.787] iteration 1018: loss: 0.744249, mix_dice: 1.405117, mix_ce: 0.083382
[15:09:48.834] iteration 1019: loss: 0.690734, mix_dice: 1.289500, mix_ce: 0.091969
[15:09:48.880] iteration 1020: loss: 0.698962, mix_dice: 1.142211, mix_ce: 0.255713
[15:09:49.354] iteration 1021: loss: 0.731901, mix_dice: 1.333817, mix_ce: 0.129985
[15:09:49.402] iteration 1022: loss: 0.717444, mix_dice: 1.255080, mix_ce: 0.179809
[15:09:49.452] iteration 1023: loss: 0.602019, mix_dice: 1.004859, mix_ce: 0.199178
[15:09:49.498] iteration 1024: loss: 0.674520, mix_dice: 1.165530, mix_ce: 0.183510
[15:09:49.997] iteration 1025: loss: 0.745140, mix_dice: 1.218390, mix_ce: 0.271890
[15:09:50.045] iteration 1026: loss: 0.555814, mix_dice: 0.951918, mix_ce: 0.159709
[15:09:50.093] iteration 1027: loss: 0.720449, mix_dice: 1.337212, mix_ce: 0.103686
[15:09:50.139] iteration 1028: loss: 0.596806, mix_dice: 1.067411, mix_ce: 0.126202
[15:09:50.582] iteration 1029: loss: 0.763891, mix_dice: 1.367906, mix_ce: 0.159875
[15:09:51.460] iteration 1030: loss: 0.586205, mix_dice: 0.987021, mix_ce: 0.185389
[15:09:51.528] iteration 1031: loss: 0.670460, mix_dice: 1.265212, mix_ce: 0.075707
[15:09:51.584] iteration 1032: loss: 0.708950, mix_dice: 1.317893, mix_ce: 0.100008
[15:09:51.640] iteration 1033: loss: 0.726249, mix_dice: 1.211096, mix_ce: 0.241402
[15:09:52.024] iteration 1034: loss: 0.662153, mix_dice: 1.122436, mix_ce: 0.201870
[15:09:52.107] iteration 1035: loss: 0.669540, mix_dice: 1.155141, mix_ce: 0.183940
[15:09:52.161] iteration 1036: loss: 0.646664, mix_dice: 1.159502, mix_ce: 0.133826
[15:09:52.227] iteration 1037: loss: 0.566393, mix_dice: 1.007193, mix_ce: 0.125592
[15:09:52.694] iteration 1038: loss: 0.600607, mix_dice: 1.075589, mix_ce: 0.125625
[15:09:52.760] iteration 1039: loss: 0.524788, mix_dice: 0.910235, mix_ce: 0.139341
[15:09:52.824] iteration 1040: loss: 0.511555, mix_dice: 0.906462, mix_ce: 0.116648
[15:09:52.887] iteration 1041: loss: 0.706702, mix_dice: 1.214470, mix_ce: 0.198934
[15:09:53.270] iteration 1042: loss: 0.752576, mix_dice: 1.376523, mix_ce: 0.128629
[15:09:53.320] iteration 1043: loss: 0.620027, mix_dice: 1.065322, mix_ce: 0.174732
[15:09:53.370] iteration 1044: loss: 0.676159, mix_dice: 1.179651, mix_ce: 0.172667
[15:09:53.422] iteration 1045: loss: 0.683734, mix_dice: 1.199686, mix_ce: 0.167782
[15:09:53.889] iteration 1046: loss: 0.707469, mix_dice: 1.219896, mix_ce: 0.195043
[15:09:53.938] iteration 1047: loss: 0.682346, mix_dice: 1.196674, mix_ce: 0.168018
[15:09:53.985] iteration 1048: loss: 0.690501, mix_dice: 1.112306, mix_ce: 0.268696
[15:09:54.032] iteration 1049: loss: 0.571939, mix_dice: 1.014039, mix_ce: 0.129840
[15:09:54.463] iteration 1050: loss: 0.624404, mix_dice: 1.085345, mix_ce: 0.163463
[15:09:55.314] iteration 1051: loss: 0.613623, mix_dice: 1.039406, mix_ce: 0.187840
[15:09:55.394] iteration 1052: loss: 0.725816, mix_dice: 1.281796, mix_ce: 0.169835
[15:09:55.445] iteration 1053: loss: 0.664830, mix_dice: 1.172771, mix_ce: 0.156890
[15:09:55.497] iteration 1054: loss: 0.705848, mix_dice: 1.255141, mix_ce: 0.156555
[15:09:55.827] iteration 1055: loss: 0.667109, mix_dice: 1.111368, mix_ce: 0.222849
[15:09:55.968] iteration 1056: loss: 0.771596, mix_dice: 1.340000, mix_ce: 0.203191
[15:09:56.016] iteration 1057: loss: 0.637676, mix_dice: 1.088321, mix_ce: 0.187031
[15:09:56.064] iteration 1058: loss: 0.596435, mix_dice: 1.042457, mix_ce: 0.150413
[15:09:56.465] iteration 1059: loss: 0.646708, mix_dice: 1.111422, mix_ce: 0.181994
[15:09:56.542] iteration 1060: loss: 0.702372, mix_dice: 1.248523, mix_ce: 0.156220
[15:09:56.591] iteration 1061: loss: 0.647286, mix_dice: 1.163811, mix_ce: 0.130760
[15:09:56.639] iteration 1062: loss: 0.725986, mix_dice: 1.319296, mix_ce: 0.132676
[15:09:57.055] iteration 1063: loss: 0.629065, mix_dice: 1.121461, mix_ce: 0.136670
[15:09:57.104] iteration 1064: loss: 0.696954, mix_dice: 1.251177, mix_ce: 0.142731
[15:09:57.154] iteration 1065: loss: 0.645950, mix_dice: 1.033720, mix_ce: 0.258180
[15:09:57.204] iteration 1066: loss: 0.545015, mix_dice: 0.959558, mix_ce: 0.130472
[15:09:57.670] iteration 1067: loss: 0.639105, mix_dice: 1.085279, mix_ce: 0.192932
[15:09:57.719] iteration 1068: loss: 0.625453, mix_dice: 1.155212, mix_ce: 0.095694
[15:09:57.766] iteration 1069: loss: 0.626878, mix_dice: 1.097606, mix_ce: 0.156149
[15:09:57.813] iteration 1070: loss: 0.776603, mix_dice: 1.481513, mix_ce: 0.071693
[15:09:58.226] iteration 1071: loss: 0.629738, mix_dice: 1.123193, mix_ce: 0.136283
[15:09:59.133] iteration 1072: loss: 0.694974, mix_dice: 1.236755, mix_ce: 0.153192
[15:09:59.184] iteration 1073: loss: 0.485503, mix_dice: 0.890086, mix_ce: 0.080921
[15:09:59.235] iteration 1074: loss: 0.645473, mix_dice: 1.093919, mix_ce: 0.197027
[15:09:59.284] iteration 1075: loss: 0.698658, mix_dice: 1.185705, mix_ce: 0.211611
[15:09:59.724] iteration 1076: loss: 0.713383, mix_dice: 1.218871, mix_ce: 0.207895
[15:09:59.789] iteration 1077: loss: 0.606987, mix_dice: 1.059072, mix_ce: 0.154902
[15:09:59.858] iteration 1078: loss: 0.769408, mix_dice: 1.388031, mix_ce: 0.150786
[15:09:59.917] iteration 1079: loss: 0.585864, mix_dice: 1.038558, mix_ce: 0.133170
[15:10:00.378] iteration 1080: loss: 0.599620, mix_dice: 0.998967, mix_ce: 0.200274
[15:10:00.431] iteration 1081: loss: 0.526769, mix_dice: 0.943826, mix_ce: 0.109711
[15:10:00.479] iteration 1082: loss: 0.619791, mix_dice: 1.030620, mix_ce: 0.208962
[15:10:00.528] iteration 1083: loss: 0.728443, mix_dice: 1.288170, mix_ce: 0.168716
[15:10:01.004] iteration 1084: loss: 0.748209, mix_dice: 1.374936, mix_ce: 0.121482
[15:10:01.056] iteration 1085: loss: 0.572743, mix_dice: 1.056585, mix_ce: 0.088901
[15:10:01.105] iteration 1086: loss: 0.542162, mix_dice: 0.908468, mix_ce: 0.175856
[15:10:01.153] iteration 1087: loss: 0.652806, mix_dice: 1.198542, mix_ce: 0.107070
[15:10:01.641] iteration 1088: loss: 0.642944, mix_dice: 1.179654, mix_ce: 0.106234
[15:10:01.691] iteration 1089: loss: 0.721909, mix_dice: 1.259037, mix_ce: 0.184780
[15:10:01.741] iteration 1090: loss: 0.638090, mix_dice: 1.120873, mix_ce: 0.155308
[15:10:01.795] iteration 1091: loss: 0.702174, mix_dice: 1.301981, mix_ce: 0.102368
[15:10:02.233] iteration 1092: loss: 0.597080, mix_dice: 1.065493, mix_ce: 0.128667
[15:10:03.102] iteration 1093: loss: 0.623600, mix_dice: 1.049986, mix_ce: 0.197215
[15:10:03.153] iteration 1094: loss: 0.659479, mix_dice: 1.155975, mix_ce: 0.162984
[15:10:03.206] iteration 1095: loss: 0.761133, mix_dice: 1.443379, mix_ce: 0.078886
[15:10:03.258] iteration 1096: loss: 0.614095, mix_dice: 1.032425, mix_ce: 0.195764
[15:10:03.639] iteration 1097: loss: 0.571179, mix_dice: 0.995720, mix_ce: 0.146638
[15:10:03.691] iteration 1098: loss: 0.552371, mix_dice: 0.990840, mix_ce: 0.113903
[15:10:03.749] iteration 1099: loss: 0.792015, mix_dice: 1.417381, mix_ce: 0.166648
[15:10:03.815] iteration 1100: loss: 0.599093, mix_dice: 1.081076, mix_ce: 0.117109
[15:10:04.294] iteration 1101: loss: 0.575557, mix_dice: 1.065918, mix_ce: 0.085196
[15:10:04.349] iteration 1102: loss: 0.738195, mix_dice: 1.384709, mix_ce: 0.091681
[15:10:04.402] iteration 1103: loss: 0.651505, mix_dice: 1.073425, mix_ce: 0.229585
[15:10:04.451] iteration 1104: loss: 0.520032, mix_dice: 0.952473, mix_ce: 0.087591
[15:10:04.907] iteration 1105: loss: 0.392756, mix_dice: 0.707930, mix_ce: 0.077582
[15:10:04.980] iteration 1106: loss: 0.606751, mix_dice: 1.035682, mix_ce: 0.177820
[15:10:05.048] iteration 1107: loss: 0.616001, mix_dice: 1.014466, mix_ce: 0.217536
[15:10:05.110] iteration 1108: loss: 0.684666, mix_dice: 1.238293, mix_ce: 0.131038
[15:10:05.478] iteration 1109: loss: 0.573900, mix_dice: 0.974948, mix_ce: 0.172852
[15:10:05.534] iteration 1110: loss: 0.622398, mix_dice: 1.114646, mix_ce: 0.130149
[15:10:05.600] iteration 1111: loss: 0.762811, mix_dice: 1.335402, mix_ce: 0.190220
[15:10:05.663] iteration 1112: loss: 0.787259, mix_dice: 1.391904, mix_ce: 0.182613
[15:10:06.066] iteration 1113: loss: 0.722368, mix_dice: 1.309680, mix_ce: 0.135055
[15:10:06.976] iteration 1114: loss: 0.550019, mix_dice: 0.991790, mix_ce: 0.108247
[15:10:07.038] iteration 1115: loss: 0.558445, mix_dice: 1.025051, mix_ce: 0.091838
[15:10:07.129] iteration 1116: loss: 0.630510, mix_dice: 1.163827, mix_ce: 0.097193
[15:10:07.201] iteration 1117: loss: 0.647169, mix_dice: 1.229199, mix_ce: 0.065140
[15:10:07.569] iteration 1118: loss: 0.602961, mix_dice: 1.125368, mix_ce: 0.080553
[15:10:07.632] iteration 1119: loss: 0.761441, mix_dice: 1.353071, mix_ce: 0.169810
[15:10:07.685] iteration 1120: loss: 0.599288, mix_dice: 1.040718, mix_ce: 0.157857
[15:10:07.749] iteration 1121: loss: 0.435709, mix_dice: 0.742273, mix_ce: 0.129146
[15:10:08.263] iteration 1122: loss: 0.677932, mix_dice: 1.241986, mix_ce: 0.113879
[15:10:08.328] iteration 1123: loss: 0.700311, mix_dice: 1.248555, mix_ce: 0.152068
[15:10:08.396] iteration 1124: loss: 0.671529, mix_dice: 1.258039, mix_ce: 0.085020
[15:10:08.500] iteration 1125: loss: 0.622980, mix_dice: 0.944504, mix_ce: 0.301455
[15:10:08.904] iteration 1126: loss: 0.602493, mix_dice: 1.061166, mix_ce: 0.143821
[15:10:08.985] iteration 1127: loss: 0.690417, mix_dice: 1.208598, mix_ce: 0.172236
[15:10:09.048] iteration 1128: loss: 0.623269, mix_dice: 1.121329, mix_ce: 0.125210
[15:10:09.101] iteration 1129: loss: 0.659291, mix_dice: 1.233193, mix_ce: 0.085388
[15:10:09.510] iteration 1130: loss: 0.668028, mix_dice: 1.221339, mix_ce: 0.114717
[15:10:09.584] iteration 1131: loss: 0.607361, mix_dice: 1.074892, mix_ce: 0.139830
[15:10:09.665] iteration 1132: loss: 0.599790, mix_dice: 1.029623, mix_ce: 0.169956
[15:10:09.733] iteration 1133: loss: 0.686589, mix_dice: 1.232658, mix_ce: 0.140521
[15:10:10.069] iteration 1134: loss: 0.653689, mix_dice: 1.139355, mix_ce: 0.168023
[15:10:11.044] iteration 1135: loss: 0.547989, mix_dice: 1.010062, mix_ce: 0.085917
[15:10:11.132] iteration 1136: loss: 0.601502, mix_dice: 1.048717, mix_ce: 0.154288
[15:10:11.235] iteration 1137: loss: 0.661477, mix_dice: 1.194917, mix_ce: 0.128037
[15:10:11.303] iteration 1138: loss: 0.654041, mix_dice: 1.097883, mix_ce: 0.210200
[15:10:11.648] iteration 1139: loss: 0.747184, mix_dice: 1.319992, mix_ce: 0.174376
[15:10:11.704] iteration 1140: loss: 0.767941, mix_dice: 1.359335, mix_ce: 0.176547
[15:10:11.772] iteration 1141: loss: 0.570981, mix_dice: 0.989860, mix_ce: 0.152101
[15:10:11.826] iteration 1142: loss: 0.813044, mix_dice: 1.482587, mix_ce: 0.143501
[15:10:12.286] iteration 1143: loss: 0.590213, mix_dice: 1.025737, mix_ce: 0.154690
[15:10:12.335] iteration 1144: loss: 0.456958, mix_dice: 0.776678, mix_ce: 0.137237
[15:10:12.388] iteration 1145: loss: 0.816652, mix_dice: 1.546326, mix_ce: 0.086978
[15:10:12.441] iteration 1146: loss: 0.814860, mix_dice: 1.431451, mix_ce: 0.198268
[15:10:12.934] iteration 1147: loss: 0.687402, mix_dice: 1.246536, mix_ce: 0.128267
[15:10:12.998] iteration 1148: loss: 0.618361, mix_dice: 1.014312, mix_ce: 0.222409
[15:10:13.053] iteration 1149: loss: 0.642963, mix_dice: 1.089348, mix_ce: 0.196578
[15:10:13.107] iteration 1150: loss: 0.746475, mix_dice: 1.372214, mix_ce: 0.120737
[15:10:13.576] iteration 1151: loss: 0.520297, mix_dice: 0.898680, mix_ce: 0.141913
[15:10:13.635] iteration 1152: loss: 0.606359, mix_dice: 1.085902, mix_ce: 0.126816
[15:10:13.685] iteration 1153: loss: 0.738121, mix_dice: 1.179103, mix_ce: 0.297138
[15:10:13.746] iteration 1154: loss: 0.524004, mix_dice: 0.940991, mix_ce: 0.107016
[15:10:14.168] iteration 1155: loss: 0.629293, mix_dice: 1.149353, mix_ce: 0.109233
[15:10:15.072] iteration 1156: loss: 0.715866, mix_dice: 1.311432, mix_ce: 0.120301
[15:10:15.123] iteration 1157: loss: 0.625908, mix_dice: 1.019506, mix_ce: 0.232311
[15:10:15.171] iteration 1158: loss: 0.567349, mix_dice: 0.949319, mix_ce: 0.185379
[15:10:15.219] iteration 1159: loss: 0.664017, mix_dice: 1.201379, mix_ce: 0.126656
[15:10:15.626] iteration 1160: loss: 0.617465, mix_dice: 1.080724, mix_ce: 0.154205
[15:10:15.727] iteration 1161: loss: 0.687345, mix_dice: 1.227304, mix_ce: 0.147386
[15:10:15.777] iteration 1162: loss: 0.702946, mix_dice: 1.313825, mix_ce: 0.092067
[15:10:15.825] iteration 1163: loss: 0.720052, mix_dice: 1.302034, mix_ce: 0.138070
[15:10:16.384] iteration 1164: loss: 0.611162, mix_dice: 1.118317, mix_ce: 0.104007
[15:10:16.438] iteration 1165: loss: 0.637057, mix_dice: 1.127701, mix_ce: 0.146414
[15:10:16.489] iteration 1166: loss: 0.623916, mix_dice: 1.120344, mix_ce: 0.127488
[15:10:16.546] iteration 1167: loss: 0.563938, mix_dice: 1.029067, mix_ce: 0.098810
[15:10:16.976] iteration 1168: loss: 0.604073, mix_dice: 0.981390, mix_ce: 0.226756
[15:10:17.024] iteration 1169: loss: 0.438080, mix_dice: 0.734701, mix_ce: 0.141460
[15:10:17.071] iteration 1170: loss: 0.604417, mix_dice: 1.037316, mix_ce: 0.171518
[15:10:17.122] iteration 1171: loss: 0.674158, mix_dice: 1.230413, mix_ce: 0.117903
[15:10:17.582] iteration 1172: loss: 0.465583, mix_dice: 0.804115, mix_ce: 0.127050
[15:10:17.638] iteration 1173: loss: 0.650822, mix_dice: 1.197403, mix_ce: 0.104241
[15:10:17.711] iteration 1174: loss: 0.717083, mix_dice: 1.238774, mix_ce: 0.195392
[15:10:17.783] iteration 1175: loss: 0.649845, mix_dice: 1.203207, mix_ce: 0.096484
[15:10:18.143] iteration 1176: loss: 0.520501, mix_dice: 0.916655, mix_ce: 0.124347
[15:10:19.058] iteration 1177: loss: 0.631272, mix_dice: 1.111227, mix_ce: 0.151318
[15:10:19.107] iteration 1178: loss: 0.570235, mix_dice: 0.985936, mix_ce: 0.154534
[15:10:19.155] iteration 1179: loss: 0.511905, mix_dice: 0.932827, mix_ce: 0.090982
[15:10:19.202] iteration 1180: loss: 0.668135, mix_dice: 1.225490, mix_ce: 0.110779
[15:10:19.613] iteration 1181: loss: 0.805224, mix_dice: 1.253746, mix_ce: 0.356702
[15:10:19.663] iteration 1182: loss: 0.666847, mix_dice: 1.247750, mix_ce: 0.085944
[15:10:19.711] iteration 1183: loss: 0.664279, mix_dice: 1.068195, mix_ce: 0.260364
[15:10:19.760] iteration 1184: loss: 0.541218, mix_dice: 0.978499, mix_ce: 0.103937
[15:10:20.296] iteration 1185: loss: 0.618370, mix_dice: 1.107362, mix_ce: 0.129377
[15:10:20.346] iteration 1186: loss: 0.499757, mix_dice: 0.862939, mix_ce: 0.136576
[15:10:20.393] iteration 1187: loss: 0.727488, mix_dice: 1.324964, mix_ce: 0.130012
[15:10:20.440] iteration 1188: loss: 0.545078, mix_dice: 0.963754, mix_ce: 0.126403
[15:10:20.918] iteration 1189: loss: 0.739910, mix_dice: 1.316215, mix_ce: 0.163605
[15:10:20.966] iteration 1190: loss: 0.503670, mix_dice: 0.870264, mix_ce: 0.137077
[15:10:21.018] iteration 1191: loss: 0.480384, mix_dice: 0.844354, mix_ce: 0.116414
[15:10:21.065] iteration 1192: loss: 0.601424, mix_dice: 1.067037, mix_ce: 0.135811
[15:10:21.522] iteration 1193: loss: 0.629057, mix_dice: 1.073616, mix_ce: 0.184499
[15:10:21.570] iteration 1194: loss: 0.699833, mix_dice: 1.281407, mix_ce: 0.118259
[15:10:21.617] iteration 1195: loss: 0.485479, mix_dice: 0.872257, mix_ce: 0.098702
[15:10:21.663] iteration 1196: loss: 0.547614, mix_dice: 0.974919, mix_ce: 0.120309
[15:10:22.091] iteration 1197: loss: 0.539884, mix_dice: 0.892393, mix_ce: 0.187375
[15:10:22.952] iteration 1198: loss: 0.531107, mix_dice: 0.919712, mix_ce: 0.142501
[15:10:23.000] iteration 1199: loss: 0.617545, mix_dice: 1.178691, mix_ce: 0.056398
[15:10:23.050] iteration 1200: loss: 0.510928, mix_dice: 0.933006, mix_ce: 0.088849
[15:10:23.097] iteration 1201: loss: 0.521725, mix_dice: 0.919356, mix_ce: 0.124094
[15:10:23.474] iteration 1202: loss: 0.569262, mix_dice: 0.999895, mix_ce: 0.138630
[15:10:23.643] iteration 1203: loss: 0.522709, mix_dice: 0.915234, mix_ce: 0.130184
[15:10:23.691] iteration 1204: loss: 0.609134, mix_dice: 1.131175, mix_ce: 0.087093
[15:10:23.739] iteration 1205: loss: 0.535596, mix_dice: 0.963243, mix_ce: 0.107950
[15:10:24.099] iteration 1206: loss: 0.676211, mix_dice: 1.153707, mix_ce: 0.198715
[15:10:24.274] iteration 1207: loss: 0.667256, mix_dice: 1.088608, mix_ce: 0.245903
[15:10:24.326] iteration 1208: loss: 0.708771, mix_dice: 1.294275, mix_ce: 0.123266
[15:10:24.379] iteration 1209: loss: 0.668835, mix_dice: 1.138565, mix_ce: 0.199106
[15:10:24.712] iteration 1210: loss: 0.560451, mix_dice: 0.984394, mix_ce: 0.136509
[15:10:24.839] iteration 1211: loss: 0.498306, mix_dice: 0.896727, mix_ce: 0.099886
[15:10:24.889] iteration 1212: loss: 0.582480, mix_dice: 1.059671, mix_ce: 0.105289
[15:10:24.940] iteration 1213: loss: 0.642553, mix_dice: 1.111928, mix_ce: 0.173177
[15:10:25.322] iteration 1214: loss: 0.407768, mix_dice: 0.664740, mix_ce: 0.150797
[15:10:25.438] iteration 1215: loss: 0.595250, mix_dice: 1.038337, mix_ce: 0.152164
[15:10:25.492] iteration 1216: loss: 0.663635, mix_dice: 1.159008, mix_ce: 0.168263
[15:10:25.545] iteration 1217: loss: 0.620262, mix_dice: 1.089654, mix_ce: 0.150870
[15:10:25.911] iteration 1218: loss: 0.623838, mix_dice: 1.036834, mix_ce: 0.210842
[15:10:26.842] iteration 1219: loss: 0.565455, mix_dice: 0.963575, mix_ce: 0.167335
[15:10:26.898] iteration 1220: loss: 0.564532, mix_dice: 1.019418, mix_ce: 0.109645
[15:10:26.954] iteration 1221: loss: 0.647453, mix_dice: 1.119101, mix_ce: 0.175805
[15:10:27.017] iteration 1222: loss: 0.747137, mix_dice: 1.329728, mix_ce: 0.164546
[15:10:27.402] iteration 1223: loss: 0.556449, mix_dice: 0.963866, mix_ce: 0.149032
[15:10:27.457] iteration 1224: loss: 0.615879, mix_dice: 1.021591, mix_ce: 0.210167
[15:10:27.511] iteration 1225: loss: 0.613948, mix_dice: 1.122506, mix_ce: 0.105391
[15:10:27.562] iteration 1226: loss: 0.786806, mix_dice: 1.465802, mix_ce: 0.107810
[15:10:28.064] iteration 1227: loss: 0.628776, mix_dice: 1.142291, mix_ce: 0.115261
[15:10:28.116] iteration 1228: loss: 0.526206, mix_dice: 0.973468, mix_ce: 0.078945
[15:10:28.166] iteration 1229: loss: 0.470735, mix_dice: 0.830495, mix_ce: 0.110974
[15:10:28.217] iteration 1230: loss: 0.642554, mix_dice: 1.204907, mix_ce: 0.080202
[15:10:28.671] iteration 1231: loss: 0.475452, mix_dice: 0.756168, mix_ce: 0.194737
[15:10:28.727] iteration 1232: loss: 0.615875, mix_dice: 1.144921, mix_ce: 0.086828
[15:10:28.784] iteration 1233: loss: 0.568977, mix_dice: 1.025246, mix_ce: 0.112709
[15:10:28.837] iteration 1234: loss: 0.636644, mix_dice: 1.107511, mix_ce: 0.165776
[15:10:29.243] iteration 1235: loss: 0.523853, mix_dice: 0.935013, mix_ce: 0.112693
[15:10:29.291] iteration 1236: loss: 0.465120, mix_dice: 0.822701, mix_ce: 0.107538
[15:10:29.340] iteration 1237: loss: 0.604476, mix_dice: 1.082290, mix_ce: 0.126663
[15:10:29.387] iteration 1238: loss: 0.725849, mix_dice: 1.319316, mix_ce: 0.132382
[15:10:29.798] iteration 1239: loss: 0.630278, mix_dice: 1.127007, mix_ce: 0.133549
[15:10:30.715] iteration 1240: loss: 0.605810, mix_dice: 1.138384, mix_ce: 0.073235
[15:10:30.772] iteration 1241: loss: 0.512413, mix_dice: 0.934369, mix_ce: 0.090458
[15:10:30.825] iteration 1242: loss: 0.483412, mix_dice: 0.881832, mix_ce: 0.084991
[15:10:30.879] iteration 1243: loss: 0.633047, mix_dice: 1.164689, mix_ce: 0.101406
[15:10:31.230] iteration 1244: loss: 0.485977, mix_dice: 0.849941, mix_ce: 0.122013
[15:10:31.277] iteration 1245: loss: 0.622208, mix_dice: 1.060403, mix_ce: 0.184012
[15:10:31.325] iteration 1246: loss: 0.787210, mix_dice: 1.453564, mix_ce: 0.120855
[15:10:31.423] iteration 1247: loss: 0.384100, mix_dice: 0.636625, mix_ce: 0.131575
[15:10:31.894] iteration 1248: loss: 0.522877, mix_dice: 0.949385, mix_ce: 0.096369
[15:10:31.945] iteration 1249: loss: 0.612514, mix_dice: 1.006567, mix_ce: 0.218461
[15:10:31.994] iteration 1250: loss: 0.618869, mix_dice: 1.120251, mix_ce: 0.117488
[15:10:32.043] iteration 1251: loss: 0.539014, mix_dice: 0.919729, mix_ce: 0.158298
[15:10:32.523] iteration 1252: loss: 0.602995, mix_dice: 1.075690, mix_ce: 0.130300
[15:10:32.588] iteration 1253: loss: 0.658890, mix_dice: 1.170356, mix_ce: 0.147425
[15:10:32.649] iteration 1254: loss: 0.441993, mix_dice: 0.748775, mix_ce: 0.135211
[15:10:32.713] iteration 1255: loss: 0.593151, mix_dice: 1.094994, mix_ce: 0.091309
[15:10:33.112] iteration 1256: loss: 0.550312, mix_dice: 0.995136, mix_ce: 0.105488
[15:10:33.167] iteration 1257: loss: 0.687880, mix_dice: 1.248066, mix_ce: 0.127694
[15:10:33.226] iteration 1258: loss: 0.671941, mix_dice: 1.209642, mix_ce: 0.134239
[15:10:33.299] iteration 1259: loss: 0.501751, mix_dice: 0.917961, mix_ce: 0.085540
[15:10:33.715] iteration 1260: loss: 0.436445, mix_dice: 0.807879, mix_ce: 0.065011
[15:10:34.646] iteration 1261: loss: 0.729206, mix_dice: 1.250477, mix_ce: 0.207935
[15:10:34.695] iteration 1262: loss: 0.574569, mix_dice: 0.997799, mix_ce: 0.151339
[15:10:34.753] iteration 1263: loss: 0.728164, mix_dice: 1.219048, mix_ce: 0.237279
[15:10:34.814] iteration 1264: loss: 0.755316, mix_dice: 1.338334, mix_ce: 0.172297
[15:10:35.172] iteration 1265: loss: 0.733383, mix_dice: 1.292085, mix_ce: 0.174681
[15:10:35.279] iteration 1266: loss: 0.703169, mix_dice: 1.300120, mix_ce: 0.106218
[15:10:35.341] iteration 1267: loss: 0.499891, mix_dice: 0.863753, mix_ce: 0.136030
[15:10:35.391] iteration 1268: loss: 0.708510, mix_dice: 1.261498, mix_ce: 0.155521
[15:10:35.825] iteration 1269: loss: 0.523386, mix_dice: 0.911217, mix_ce: 0.135555
[15:10:35.874] iteration 1270: loss: 0.709350, mix_dice: 1.318823, mix_ce: 0.099877
[15:10:35.923] iteration 1271: loss: 0.700843, mix_dice: 1.272973, mix_ce: 0.128714
[15:10:35.984] iteration 1272: loss: 0.686571, mix_dice: 1.229379, mix_ce: 0.143762
[15:10:36.468] iteration 1273: loss: 0.822355, mix_dice: 1.344148, mix_ce: 0.300563
[15:10:36.529] iteration 1274: loss: 0.647826, mix_dice: 1.166706, mix_ce: 0.128945
[15:10:36.590] iteration 1275: loss: 0.470709, mix_dice: 0.779870, mix_ce: 0.161549
[15:10:36.671] iteration 1276: loss: 0.647940, mix_dice: 1.174432, mix_ce: 0.121448
[15:10:37.067] iteration 1277: loss: 0.602664, mix_dice: 1.081933, mix_ce: 0.123394
[15:10:37.136] iteration 1278: loss: 0.524834, mix_dice: 0.955693, mix_ce: 0.093974
[15:10:37.219] iteration 1279: loss: 0.612499, mix_dice: 1.001761, mix_ce: 0.223237
[15:10:37.291] iteration 1280: loss: 0.592119, mix_dice: 1.027165, mix_ce: 0.157074
[15:10:37.623] iteration 1281: loss: 0.560069, mix_dice: 0.964943, mix_ce: 0.155196
[15:10:38.458] iteration 1282: loss: 0.618984, mix_dice: 1.076562, mix_ce: 0.161406
[15:10:38.591] iteration 1283: loss: 0.515488, mix_dice: 0.911443, mix_ce: 0.119534
[15:10:38.649] iteration 1284: loss: 0.593237, mix_dice: 1.080533, mix_ce: 0.105940
[15:10:38.697] iteration 1285: loss: 0.603120, mix_dice: 0.986372, mix_ce: 0.219868
[15:10:38.994] iteration 1286: loss: 0.754933, mix_dice: 1.344792, mix_ce: 0.165073
[15:10:39.178] iteration 1287: loss: 0.765586, mix_dice: 1.295727, mix_ce: 0.235445
[15:10:39.232] iteration 1288: loss: 0.605232, mix_dice: 1.005575, mix_ce: 0.204889
[15:10:39.285] iteration 1289: loss: 0.685160, mix_dice: 1.280568, mix_ce: 0.089752
[15:10:39.643] iteration 1290: loss: 0.570451, mix_dice: 1.018447, mix_ce: 0.122454
[15:10:39.818] iteration 1291: loss: 0.615344, mix_dice: 1.101662, mix_ce: 0.129025
[15:10:39.869] iteration 1292: loss: 0.592685, mix_dice: 1.062752, mix_ce: 0.122618
[15:10:39.917] iteration 1293: loss: 0.477180, mix_dice: 0.843707, mix_ce: 0.110653
[15:10:40.264] iteration 1294: loss: 0.566555, mix_dice: 1.007181, mix_ce: 0.125929
[15:10:40.405] iteration 1295: loss: 0.615058, mix_dice: 1.131161, mix_ce: 0.098955
[15:10:40.455] iteration 1296: loss: 0.749069, mix_dice: 1.405568, mix_ce: 0.092569
[15:10:40.503] iteration 1297: loss: 0.786297, mix_dice: 1.400192, mix_ce: 0.172403
[15:10:40.865] iteration 1298: loss: 0.580855, mix_dice: 1.056326, mix_ce: 0.105383
[15:10:41.014] iteration 1299: loss: 0.699560, mix_dice: 1.276370, mix_ce: 0.122751
[15:10:41.065] iteration 1300: loss: 0.703447, mix_dice: 1.211468, mix_ce: 0.195426
[15:10:41.113] iteration 1301: loss: 0.730316, mix_dice: 1.199337, mix_ce: 0.261294
[15:10:41.435] iteration 1302: loss: 0.709580, mix_dice: 1.208871, mix_ce: 0.210289
[15:10:42.382] iteration 1303: loss: 0.712307, mix_dice: 1.331396, mix_ce: 0.093217
[15:10:42.454] iteration 1304: loss: 0.574370, mix_dice: 1.010897, mix_ce: 0.137842
[15:10:42.519] iteration 1305: loss: 0.670560, mix_dice: 1.200082, mix_ce: 0.141037
[15:10:42.600] iteration 1306: loss: 0.559098, mix_dice: 0.884537, mix_ce: 0.233659
[15:10:42.909] iteration 1307: loss: 0.587378, mix_dice: 1.017079, mix_ce: 0.157676
[15:10:43.002] iteration 1308: loss: 0.664380, mix_dice: 1.149256, mix_ce: 0.179505
[15:10:43.070] iteration 1309: loss: 0.542380, mix_dice: 0.898837, mix_ce: 0.185924
[15:10:43.145] iteration 1310: loss: 0.693719, mix_dice: 1.257120, mix_ce: 0.130318
[15:10:43.583] iteration 1311: loss: 0.618423, mix_dice: 1.129279, mix_ce: 0.107568
[15:10:43.652] iteration 1312: loss: 0.646662, mix_dice: 1.147707, mix_ce: 0.145617
[15:10:43.711] iteration 1313: loss: 0.685938, mix_dice: 1.284202, mix_ce: 0.087674
[15:10:43.804] iteration 1314: loss: 0.670981, mix_dice: 1.163209, mix_ce: 0.178752
[15:10:44.247] iteration 1315: loss: 0.548797, mix_dice: 0.950431, mix_ce: 0.147163
[15:10:44.345] iteration 1316: loss: 0.623347, mix_dice: 1.108119, mix_ce: 0.138576
[15:10:44.458] iteration 1317: loss: 0.794455, mix_dice: 1.443273, mix_ce: 0.145638
[15:10:44.580] iteration 1318: loss: 0.727507, mix_dice: 1.304320, mix_ce: 0.150694
[15:10:44.748] iteration 1319: loss: 0.590407, mix_dice: 1.075623, mix_ce: 0.105191
[15:10:44.837] iteration 1320: loss: 0.668986, mix_dice: 0.994808, mix_ce: 0.343165
[15:10:44.983] iteration 1321: loss: 0.759973, mix_dice: 1.400319, mix_ce: 0.119626
[15:10:45.133] iteration 1322: loss: 0.536033, mix_dice: 0.930706, mix_ce: 0.141361
[15:10:45.332] iteration 1323: loss: 0.721919, mix_dice: 1.285888, mix_ce: 0.157949
[15:10:46.571] iteration 1324: loss: 0.779160, mix_dice: 1.488812, mix_ce: 0.069508
[15:10:46.663] iteration 1325: loss: 0.731263, mix_dice: 1.389154, mix_ce: 0.073371
[15:10:46.748] iteration 1326: loss: 0.502285, mix_dice: 0.890355, mix_ce: 0.114214
[15:10:46.826] iteration 1327: loss: 0.692441, mix_dice: 1.247456, mix_ce: 0.137426
[15:10:47.211] iteration 1328: loss: 0.724560, mix_dice: 1.327862, mix_ce: 0.121258
[15:10:47.325] iteration 1329: loss: 0.642699, mix_dice: 1.056444, mix_ce: 0.228954
[15:10:47.404] iteration 1330: loss: 0.506324, mix_dice: 0.874566, mix_ce: 0.138082
[15:10:47.462] iteration 1331: loss: 0.658741, mix_dice: 1.207345, mix_ce: 0.110136
[15:10:47.618] iteration 1332: loss: 0.634577, mix_dice: 1.082157, mix_ce: 0.186996
[15:10:47.771] iteration 1333: loss: 0.556820, mix_dice: 0.918067, mix_ce: 0.195573
[15:10:47.867] iteration 1334: loss: 0.619287, mix_dice: 1.059510, mix_ce: 0.179065
[15:10:47.936] iteration 1335: loss: 0.534585, mix_dice: 0.925982, mix_ce: 0.143188
[15:10:48.285] iteration 1336: loss: 0.697193, mix_dice: 1.319746, mix_ce: 0.074639
[15:10:48.392] iteration 1337: loss: 0.616738, mix_dice: 1.138298, mix_ce: 0.095179
[15:10:48.580] iteration 1338: loss: 0.534486, mix_dice: 0.977826, mix_ce: 0.091146
[15:10:48.769] iteration 1339: loss: 0.588126, mix_dice: 1.020815, mix_ce: 0.155438
[15:10:48.927] iteration 1340: loss: 0.669996, mix_dice: 1.194217, mix_ce: 0.145776
[15:10:49.019] iteration 1341: loss: 0.582914, mix_dice: 1.024599, mix_ce: 0.141229
[15:10:49.108] iteration 1342: loss: 0.493268, mix_dice: 0.861221, mix_ce: 0.125314
[15:10:49.186] iteration 1343: loss: 0.610425, mix_dice: 1.040105, mix_ce: 0.180744
[15:10:49.442] iteration 1344: loss: 0.538749, mix_dice: 0.967474, mix_ce: 0.110024
[15:10:50.521] iteration 1345: loss: 0.531843, mix_dice: 0.938760, mix_ce: 0.124926
[15:10:50.618] iteration 1346: loss: 0.441161, mix_dice: 0.790809, mix_ce: 0.091512
[15:10:50.714] iteration 1347: loss: 0.591822, mix_dice: 1.091496, mix_ce: 0.092148
[15:10:50.823] iteration 1348: loss: 0.731493, mix_dice: 1.291830, mix_ce: 0.171156
[15:10:51.054] iteration 1349: loss: 0.523613, mix_dice: 0.884978, mix_ce: 0.162248
[15:10:51.125] iteration 1350: loss: 0.708619, mix_dice: 1.312774, mix_ce: 0.104464
[15:10:51.200] iteration 1351: loss: 0.542196, mix_dice: 0.968965, mix_ce: 0.115428
[15:10:51.258] iteration 1352: loss: 0.499065, mix_dice: 0.870293, mix_ce: 0.127836
[15:10:51.721] iteration 1353: loss: 0.675561, mix_dice: 1.305556, mix_ce: 0.045565
[15:10:51.827] iteration 1354: loss: 0.711372, mix_dice: 1.344889, mix_ce: 0.077854
[15:10:51.925] iteration 1355: loss: 0.522598, mix_dice: 0.890065, mix_ce: 0.155132
[15:10:52.044] iteration 1356: loss: 0.649411, mix_dice: 1.136937, mix_ce: 0.161884
[15:10:52.283] iteration 1357: loss: 0.606657, mix_dice: 0.998804, mix_ce: 0.214510
[15:10:52.399] iteration 1358: loss: 0.465032, mix_dice: 0.759430, mix_ce: 0.170634
[15:10:52.483] iteration 1359: loss: 0.647501, mix_dice: 1.158276, mix_ce: 0.136726
[15:10:52.575] iteration 1360: loss: 0.521926, mix_dice: 0.886151, mix_ce: 0.157701
[15:10:52.884] iteration 1361: loss: 0.636883, mix_dice: 1.118586, mix_ce: 0.155181
[15:10:53.003] iteration 1362: loss: 0.894343, mix_dice: 1.648342, mix_ce: 0.140345
[15:10:53.116] iteration 1363: loss: 0.579537, mix_dice: 1.006886, mix_ce: 0.152187
[15:10:53.204] iteration 1364: loss: 0.536357, mix_dice: 0.903834, mix_ce: 0.168880
[15:10:53.418] iteration 1365: loss: 0.535369, mix_dice: 0.925792, mix_ce: 0.144945
[15:10:54.304] iteration 1366: loss: 0.580488, mix_dice: 1.032832, mix_ce: 0.128145
[15:10:54.368] iteration 1367: loss: 0.738387, mix_dice: 1.317867, mix_ce: 0.158907
[15:10:54.433] iteration 1368: loss: 0.434987, mix_dice: 0.710395, mix_ce: 0.159579
[15:10:54.504] iteration 1369: loss: 0.626597, mix_dice: 1.143697, mix_ce: 0.109497
[15:10:54.815] iteration 1370: loss: 0.639538, mix_dice: 1.120490, mix_ce: 0.158586
[15:10:54.884] iteration 1371: loss: 0.612608, mix_dice: 1.116468, mix_ce: 0.108747
[15:10:54.946] iteration 1372: loss: 0.521493, mix_dice: 0.846306, mix_ce: 0.196681
[15:10:54.996] iteration 1373: loss: 0.600829, mix_dice: 1.066061, mix_ce: 0.135597
[15:10:55.438] iteration 1374: loss: 0.439565, mix_dice: 0.786861, mix_ce: 0.092269
[15:10:55.492] iteration 1375: loss: 0.664035, mix_dice: 1.142949, mix_ce: 0.185120
[15:10:55.541] iteration 1376: loss: 0.641610, mix_dice: 1.210385, mix_ce: 0.072835
[15:10:55.589] iteration 1377: loss: 0.539730, mix_dice: 0.964344, mix_ce: 0.115116
[15:10:56.042] iteration 1378: loss: 0.791958, mix_dice: 1.428987, mix_ce: 0.154928
[15:10:56.092] iteration 1379: loss: 0.474694, mix_dice: 0.838137, mix_ce: 0.111250
[15:10:56.139] iteration 1380: loss: 0.633971, mix_dice: 1.135604, mix_ce: 0.132339
[15:10:56.188] iteration 1381: loss: 0.576825, mix_dice: 1.024652, mix_ce: 0.128997
[15:10:56.624] iteration 1382: loss: 0.653043, mix_dice: 1.222641, mix_ce: 0.083446
[15:10:56.675] iteration 1383: loss: 0.466959, mix_dice: 0.836693, mix_ce: 0.097225
[15:10:56.735] iteration 1384: loss: 0.496820, mix_dice: 0.837418, mix_ce: 0.156223
[15:10:56.791] iteration 1385: loss: 0.577264, mix_dice: 0.979182, mix_ce: 0.175346
[15:10:57.186] iteration 1386: loss: 0.569755, mix_dice: 1.037388, mix_ce: 0.102122
[15:10:58.058] iteration 1387: loss: 0.732568, mix_dice: 1.263878, mix_ce: 0.201257
[15:10:58.146] iteration 1388: loss: 0.524661, mix_dice: 0.961653, mix_ce: 0.087670
[15:10:58.202] iteration 1389: loss: 0.602133, mix_dice: 1.112382, mix_ce: 0.091884
[15:10:58.252] iteration 1390: loss: 0.682594, mix_dice: 1.252147, mix_ce: 0.113042
[15:10:58.642] iteration 1391: loss: 0.562322, mix_dice: 1.014990, mix_ce: 0.109654
[15:10:58.739] iteration 1392: loss: 0.569120, mix_dice: 0.958656, mix_ce: 0.179585
[15:10:58.807] iteration 1393: loss: 0.698766, mix_dice: 1.246756, mix_ce: 0.150775
[15:10:58.863] iteration 1394: loss: 0.541366, mix_dice: 0.990840, mix_ce: 0.091891
[15:10:59.304] iteration 1395: loss: 0.511917, mix_dice: 0.908411, mix_ce: 0.115423
[15:10:59.362] iteration 1396: loss: 0.742196, mix_dice: 1.364862, mix_ce: 0.119530
[15:10:59.422] iteration 1397: loss: 0.491592, mix_dice: 0.833031, mix_ce: 0.150153
[15:10:59.480] iteration 1398: loss: 0.727179, mix_dice: 1.317058, mix_ce: 0.137300
[15:10:59.868] iteration 1399: loss: 0.557929, mix_dice: 0.993364, mix_ce: 0.122495
[15:10:59.926] iteration 1400: loss: 0.584074, mix_dice: 1.006728, mix_ce: 0.161420
[15:10:59.983] iteration 1401: loss: 0.611151, mix_dice: 1.163917, mix_ce: 0.058385
[15:11:00.036] iteration 1402: loss: 0.705691, mix_dice: 1.243181, mix_ce: 0.168201
[15:11:00.425] iteration 1403: loss: 0.653546, mix_dice: 1.202778, mix_ce: 0.104313
[15:11:00.472] iteration 1404: loss: 0.514620, mix_dice: 0.905426, mix_ce: 0.123815
[15:11:00.518] iteration 1405: loss: 0.419622, mix_dice: 0.684323, mix_ce: 0.154921
[15:11:00.565] iteration 1406: loss: 0.407621, mix_dice: 0.699055, mix_ce: 0.116187
[15:11:00.980] iteration 1407: loss: 0.581032, mix_dice: 1.037787, mix_ce: 0.124278
[15:11:01.869] iteration 1408: loss: 0.563696, mix_dice: 0.992993, mix_ce: 0.134399
[15:11:01.950] iteration 1409: loss: 0.641614, mix_dice: 1.188906, mix_ce: 0.094321
[15:11:02.028] iteration 1410: loss: 0.630639, mix_dice: 1.159332, mix_ce: 0.101946
[15:11:02.113] iteration 1411: loss: 0.522152, mix_dice: 0.920115, mix_ce: 0.124188
[15:11:02.380] iteration 1412: loss: 0.581084, mix_dice: 1.002949, mix_ce: 0.159218
[15:11:02.459] iteration 1413: loss: 0.659877, mix_dice: 1.107403, mix_ce: 0.212352
[15:11:02.517] iteration 1414: loss: 0.638919, mix_dice: 1.166694, mix_ce: 0.111143
[15:11:02.575] iteration 1415: loss: 0.526282, mix_dice: 0.978794, mix_ce: 0.073770
[15:11:02.969] iteration 1416: loss: 0.712250, mix_dice: 1.303604, mix_ce: 0.120896
[15:11:03.017] iteration 1417: loss: 0.507588, mix_dice: 0.856341, mix_ce: 0.158835
[15:11:03.070] iteration 1418: loss: 0.688407, mix_dice: 1.169872, mix_ce: 0.206943
[15:11:03.128] iteration 1419: loss: 0.562874, mix_dice: 1.043424, mix_ce: 0.082323
[15:11:03.623] iteration 1420: loss: 0.580833, mix_dice: 1.025829, mix_ce: 0.135836
[15:11:03.728] iteration 1421: loss: 0.591978, mix_dice: 1.106078, mix_ce: 0.077878
[15:11:03.839] iteration 1422: loss: 0.506694, mix_dice: 0.801500, mix_ce: 0.211888
[15:11:03.897] iteration 1423: loss: 0.605963, mix_dice: 1.076096, mix_ce: 0.135830
[15:11:04.116] iteration 1424: loss: 0.672662, mix_dice: 1.254661, mix_ce: 0.090664
[15:11:04.164] iteration 1425: loss: 0.552117, mix_dice: 0.937777, mix_ce: 0.166456
[15:11:04.212] iteration 1426: loss: 0.569524, mix_dice: 0.964982, mix_ce: 0.174065
[15:11:04.273] iteration 1427: loss: 0.578903, mix_dice: 1.011525, mix_ce: 0.146282
[15:11:04.689] iteration 1428: loss: 0.475022, mix_dice: 0.829035, mix_ce: 0.121009
[15:11:05.595] iteration 1429: loss: 0.668618, mix_dice: 1.281565, mix_ce: 0.055671
[15:11:05.659] iteration 1430: loss: 0.572457, mix_dice: 1.020277, mix_ce: 0.124638
[15:11:05.735] iteration 1431: loss: 0.601441, mix_dice: 1.122296, mix_ce: 0.080585
[15:11:05.799] iteration 1432: loss: 0.547480, mix_dice: 0.915844, mix_ce: 0.179117
[15:11:06.080] iteration 1433: loss: 0.530917, mix_dice: 0.959449, mix_ce: 0.102385
[15:11:06.200] iteration 1434: loss: 0.636565, mix_dice: 1.164521, mix_ce: 0.108609
[15:11:06.276] iteration 1435: loss: 0.715491, mix_dice: 1.332479, mix_ce: 0.098502
[15:11:06.338] iteration 1436: loss: 0.561402, mix_dice: 0.996585, mix_ce: 0.126219
[15:11:06.701] iteration 1437: loss: 0.609108, mix_dice: 1.131729, mix_ce: 0.086487
[15:11:06.763] iteration 1438: loss: 0.656680, mix_dice: 1.220448, mix_ce: 0.092911
[15:11:06.823] iteration 1439: loss: 0.442363, mix_dice: 0.793737, mix_ce: 0.090989
[15:11:06.882] iteration 1440: loss: 0.618314, mix_dice: 1.093698, mix_ce: 0.142931
[15:11:07.303] iteration 1441: loss: 0.371601, mix_dice: 0.648134, mix_ce: 0.095068
[15:11:07.367] iteration 1442: loss: 0.613139, mix_dice: 0.939568, mix_ce: 0.286709
[15:11:07.429] iteration 1443: loss: 0.553728, mix_dice: 1.007192, mix_ce: 0.100265
[15:11:07.501] iteration 1444: loss: 0.614336, mix_dice: 1.110006, mix_ce: 0.118665
[15:11:07.886] iteration 1445: loss: 0.554838, mix_dice: 1.008339, mix_ce: 0.101338
[15:11:07.942] iteration 1446: loss: 0.819137, mix_dice: 1.513211, mix_ce: 0.125061
[15:11:07.995] iteration 1447: loss: 0.456027, mix_dice: 0.817791, mix_ce: 0.094263
[15:11:08.072] iteration 1448: loss: 0.462134, mix_dice: 0.831911, mix_ce: 0.092358
[15:11:08.446] iteration 1449: loss: 0.766481, mix_dice: 1.409439, mix_ce: 0.123523
[15:11:09.335] iteration 1450: loss: 0.608620, mix_dice: 1.087012, mix_ce: 0.130229
[15:11:09.392] iteration 1451: loss: 0.854673, mix_dice: 1.623369, mix_ce: 0.085978
[15:11:09.445] iteration 1452: loss: 0.725475, mix_dice: 1.377654, mix_ce: 0.073297
[15:11:09.502] iteration 1453: loss: 0.484225, mix_dice: 0.879997, mix_ce: 0.088452
[15:11:09.826] iteration 1454: loss: 0.529850, mix_dice: 0.905058, mix_ce: 0.154641
[15:11:09.893] iteration 1455: loss: 0.502181, mix_dice: 0.868355, mix_ce: 0.136007
[15:11:09.951] iteration 1456: loss: 0.595104, mix_dice: 1.038783, mix_ce: 0.151425
[15:11:10.005] iteration 1457: loss: 0.575666, mix_dice: 1.023652, mix_ce: 0.127679
[15:11:10.441] iteration 1458: loss: 0.454081, mix_dice: 0.746861, mix_ce: 0.161301
[15:11:10.493] iteration 1459: loss: 0.677867, mix_dice: 1.230683, mix_ce: 0.125052
[15:11:10.541] iteration 1460: loss: 0.536830, mix_dice: 0.968760, mix_ce: 0.104901
[15:11:10.587] iteration 1461: loss: 0.731406, mix_dice: 1.266399, mix_ce: 0.196413
[15:11:11.037] iteration 1462: loss: 0.476171, mix_dice: 0.780347, mix_ce: 0.171996
[15:11:11.091] iteration 1463: loss: 0.415802, mix_dice: 0.698099, mix_ce: 0.133505
[15:11:11.145] iteration 1464: loss: 0.479697, mix_dice: 0.864401, mix_ce: 0.094994
[15:11:11.200] iteration 1465: loss: 0.592730, mix_dice: 1.076214, mix_ce: 0.109246
[15:11:11.584] iteration 1466: loss: 0.703784, mix_dice: 1.281246, mix_ce: 0.126321
[15:11:11.640] iteration 1467: loss: 0.447264, mix_dice: 0.743448, mix_ce: 0.151080
[15:11:11.696] iteration 1468: loss: 0.551508, mix_dice: 0.961781, mix_ce: 0.141235
[15:11:11.753] iteration 1469: loss: 0.722655, mix_dice: 1.301043, mix_ce: 0.144268
[15:11:12.119] iteration 1470: loss: 0.731097, mix_dice: 1.333586, mix_ce: 0.128608
[15:11:13.006] iteration 1471: loss: 0.542380, mix_dice: 0.933933, mix_ce: 0.150828
[15:11:13.061] iteration 1472: loss: 0.869692, mix_dice: 1.633663, mix_ce: 0.105721
[15:11:13.117] iteration 1473: loss: 0.575903, mix_dice: 0.983057, mix_ce: 0.168749
[15:11:13.174] iteration 1474: loss: 0.647412, mix_dice: 1.189890, mix_ce: 0.104934
[15:11:13.508] iteration 1475: loss: 0.706833, mix_dice: 1.273660, mix_ce: 0.140006
[15:11:13.559] iteration 1476: loss: 0.649012, mix_dice: 1.142277, mix_ce: 0.155748
[15:11:13.608] iteration 1477: loss: 0.703436, mix_dice: 1.240393, mix_ce: 0.166480
[15:11:13.657] iteration 1478: loss: 0.523094, mix_dice: 0.944877, mix_ce: 0.101311
[15:11:14.155] iteration 1479: loss: 0.440933, mix_dice: 0.763958, mix_ce: 0.117909
[15:11:14.204] iteration 1480: loss: 0.545555, mix_dice: 0.956534, mix_ce: 0.134576
[15:11:14.254] iteration 1481: loss: 0.547526, mix_dice: 0.949842, mix_ce: 0.145210
[15:11:14.307] iteration 1482: loss: 0.526953, mix_dice: 0.940701, mix_ce: 0.113205
[15:11:14.753] iteration 1483: loss: 0.677950, mix_dice: 1.154278, mix_ce: 0.201621
[15:11:14.806] iteration 1484: loss: 0.578124, mix_dice: 1.043112, mix_ce: 0.113135
[15:11:14.860] iteration 1485: loss: 0.583130, mix_dice: 1.041079, mix_ce: 0.125182
[15:11:14.914] iteration 1486: loss: 0.587252, mix_dice: 0.961271, mix_ce: 0.213234
[15:11:15.360] iteration 1487: loss: 0.660561, mix_dice: 1.215718, mix_ce: 0.105404
[15:11:15.415] iteration 1488: loss: 0.647695, mix_dice: 1.238094, mix_ce: 0.057297
[15:11:15.477] iteration 1489: loss: 0.683320, mix_dice: 1.203130, mix_ce: 0.163511
[15:11:15.544] iteration 1490: loss: 0.434371, mix_dice: 0.796613, mix_ce: 0.072129
[15:11:16.030] iteration 1491: loss: 0.488154, mix_dice: 0.868377, mix_ce: 0.107931
[15:11:16.997] iteration 1492: loss: 0.623723, mix_dice: 1.121155, mix_ce: 0.126291
[15:11:17.116] iteration 1493: loss: 0.673857, mix_dice: 1.281183, mix_ce: 0.066532
[15:11:17.202] iteration 1494: loss: 0.440693, mix_dice: 0.793636, mix_ce: 0.087750
[15:11:17.296] iteration 1495: loss: 0.466946, mix_dice: 0.834494, mix_ce: 0.099397
[15:11:17.596] iteration 1496: loss: 0.633086, mix_dice: 1.189831, mix_ce: 0.076341
[15:11:17.772] iteration 1497: loss: 0.702943, mix_dice: 1.193021, mix_ce: 0.212864
[15:11:17.915] iteration 1498: loss: 0.620580, mix_dice: 1.063973, mix_ce: 0.177187
[15:11:18.073] iteration 1499: loss: 0.394875, mix_dice: 0.655215, mix_ce: 0.134536
[15:11:18.224] iteration 1500: loss: 0.670713, mix_dice: 1.229761, mix_ce: 0.111666
[11:44:36.653] Namespace(base_lr=0.01, batch_size=24, consistency=0.1, consistency_rampup=200.0, deterministic=1, exp='Synapse/SUMix_v3_add_adaptive_beta_max_beta2.0', labeled_bs=12, labeled_num=2, labelnum=2, magnitude=6.0, max_iterations=30000, model='unet', num_classes=9, num_labels=20, patch_size=[256, 256], pre_iterations=10000, root_path='/home/<USER>/data/synapse', s_param=6, seed=1337, skip_pretrain=0, temperature=0.8, test_interval=1500, test_list='test_vol.txt', train_list='train.txt', u_weight=0.5)
[11:44:37.412] Start pre_training
[11:44:37.412] 21 iterations per epoch
[11:44:42.098] iteration 1: loss: 3.254364, mix_dice: 1.865278, mix_ce: 4.643451
[11:44:42.225] iteration 2: loss: 3.110372, mix_dice: 1.882224, mix_ce: 4.338519
[11:44:42.358] iteration 3: loss: 2.832451, mix_dice: 1.869954, mix_ce: 3.794948
[11:44:42.550] iteration 4: loss: 2.453106, mix_dice: 1.806569, mix_ce: 3.099643
[11:44:42.783] iteration 5: loss: 2.064363, mix_dice: 1.792818, mix_ce: 2.335909
[11:44:43.337] iteration 6: loss: 1.695371, mix_dice: 1.746379, mix_ce: 1.644362
[11:44:43.744] iteration 7: loss: 1.398680, mix_dice: 1.691141, mix_ce: 1.106220
[11:44:44.027] iteration 8: loss: 1.259944, mix_dice: 1.653957, mix_ce: 0.865931
[11:44:44.186] iteration 9: loss: 1.101166, mix_dice: 1.705883, mix_ce: 0.496448
[11:44:44.318] iteration 10: loss: 1.123038, mix_dice: 1.660274, mix_ce: 0.585802
[11:44:44.484] iteration 11: loss: 1.057276, mix_dice: 1.690319, mix_ce: 0.424232
[11:44:44.660] iteration 12: loss: 1.080134, mix_dice: 1.685118, mix_ce: 0.475150
[11:44:44.830] iteration 13: loss: 1.069770, mix_dice: 1.681743, mix_ce: 0.457796
[11:44:45.042] iteration 14: loss: 1.030713, mix_dice: 1.659824, mix_ce: 0.401602
[11:44:45.307] iteration 15: loss: 0.988874, mix_dice: 1.708694, mix_ce: 0.269055
[11:44:45.612] iteration 16: loss: 1.068250, mix_dice: 1.681841, mix_ce: 0.454659
[11:44:45.725] iteration 17: loss: 0.996130, mix_dice: 1.679182, mix_ce: 0.313078
[11:44:46.108] iteration 18: loss: 0.955283, mix_dice: 1.673114, mix_ce: 0.237453
[11:44:46.525] iteration 19: loss: 0.971608, mix_dice: 1.684643, mix_ce: 0.258573
[11:44:46.690] iteration 20: loss: 0.984397, mix_dice: 1.660601, mix_ce: 0.308193
[11:44:46.795] iteration 21: loss: 0.996128, mix_dice: 1.727981, mix_ce: 0.264275
[11:44:47.990] iteration 22: loss: 0.927266, mix_dice: 1.680400, mix_ce: 0.174133
[11:44:48.143] iteration 23: loss: 0.943241, mix_dice: 1.686484, mix_ce: 0.199998
[11:44:48.323] iteration 24: loss: 1.044954, mix_dice: 1.660766, mix_ce: 0.429142
[11:44:48.455] iteration 25: loss: 1.003378, mix_dice: 1.664899, mix_ce: 0.341857
[11:44:48.606] iteration 26: loss: 0.988663, mix_dice: 1.696704, mix_ce: 0.280622
[11:44:48.771] iteration 27: loss: 0.992632, mix_dice: 1.615974, mix_ce: 0.369290
[11:44:48.903] iteration 28: loss: 1.004973, mix_dice: 1.604936, mix_ce: 0.405009
[11:44:49.008] iteration 29: loss: 0.961895, mix_dice: 1.656968, mix_ce: 0.266823
[11:44:49.131] iteration 30: loss: 0.932582, mix_dice: 1.642482, mix_ce: 0.222682
[11:44:49.200] iteration 31: loss: 1.017413, mix_dice: 1.625859, mix_ce: 0.408968
[11:44:49.303] iteration 32: loss: 0.987125, mix_dice: 1.665113, mix_ce: 0.309138
[11:44:49.374] iteration 33: loss: 1.002881, mix_dice: 1.635740, mix_ce: 0.370021
[11:44:49.879] iteration 34: loss: 0.912839, mix_dice: 1.605861, mix_ce: 0.219817
[11:44:50.002] iteration 35: loss: 0.964008, mix_dice: 1.593064, mix_ce: 0.334952
[11:44:50.209] iteration 36: loss: 0.995341, mix_dice: 1.681505, mix_ce: 0.309177
[11:44:50.368] iteration 37: loss: 0.922163, mix_dice: 1.614442, mix_ce: 0.229884
[11:44:50.608] iteration 38: loss: 0.910296, mix_dice: 1.600565, mix_ce: 0.220026
[11:44:50.685] iteration 39: loss: 1.052284, mix_dice: 1.598628, mix_ce: 0.505941
[11:44:50.770] iteration 40: loss: 0.968320, mix_dice: 1.608500, mix_ce: 0.328140
[11:44:50.832] iteration 41: loss: 0.900284, mix_dice: 1.620742, mix_ce: 0.179826
[11:44:51.461] iteration 42: loss: 0.872903, mix_dice: 1.563828, mix_ce: 0.181978
[11:44:52.958] iteration 43: loss: 0.944730, mix_dice: 1.626192, mix_ce: 0.263267
[11:44:53.373] iteration 44: loss: 0.926853, mix_dice: 1.621483, mix_ce: 0.232222
[11:44:53.667] iteration 45: loss: 0.947156, mix_dice: 1.576332, mix_ce: 0.317980
[11:44:54.019] iteration 46: loss: 0.948903, mix_dice: 1.568901, mix_ce: 0.328905
[11:44:54.180] iteration 47: loss: 0.951367, mix_dice: 1.621584, mix_ce: 0.281150
[11:44:54.247] iteration 48: loss: 0.923764, mix_dice: 1.599586, mix_ce: 0.247942
[11:44:54.323] iteration 49: loss: 0.851567, mix_dice: 1.566888, mix_ce: 0.136246
[11:44:54.405] iteration 50: loss: 0.938456, mix_dice: 1.536251, mix_ce: 0.340661
[11:44:54.639] iteration 51: loss: 0.948924, mix_dice: 1.529630, mix_ce: 0.368218
[11:44:54.958] iteration 52: loss: 0.943181, mix_dice: 1.504081, mix_ce: 0.382281
[11:44:55.235] iteration 53: loss: 0.984717, mix_dice: 1.595819, mix_ce: 0.373616
[11:44:55.498] iteration 54: loss: 0.950695, mix_dice: 1.588637, mix_ce: 0.312754
[11:44:55.731] iteration 55: loss: 0.876542, mix_dice: 1.506819, mix_ce: 0.246265
[11:44:55.866] iteration 56: loss: 0.911896, mix_dice: 1.509847, mix_ce: 0.313945
[11:44:55.930] iteration 57: loss: 0.945291, mix_dice: 1.649246, mix_ce: 0.241335
[11:44:56.014] iteration 58: loss: 0.897154, mix_dice: 1.566916, mix_ce: 0.227392
[11:44:56.099] iteration 59: loss: 0.930725, mix_dice: 1.583878, mix_ce: 0.277572
[11:44:56.189] iteration 60: loss: 0.889959, mix_dice: 1.659271, mix_ce: 0.120648
[11:44:56.282] iteration 61: loss: 0.947501, mix_dice: 1.518822, mix_ce: 0.376180
[11:44:56.356] iteration 62: loss: 0.920006, mix_dice: 1.618345, mix_ce: 0.221668
[11:44:56.454] iteration 63: loss: 0.895067, mix_dice: 1.552796, mix_ce: 0.237338
[11:44:57.566] iteration 64: loss: 0.887504, mix_dice: 1.598636, mix_ce: 0.176372
[11:44:57.676] iteration 65: loss: 0.924124, mix_dice: 1.481687, mix_ce: 0.366561
[11:44:57.795] iteration 66: loss: 0.914190, mix_dice: 1.566065, mix_ce: 0.262316
[11:44:57.901] iteration 67: loss: 0.868474, mix_dice: 1.555018, mix_ce: 0.181929
[11:44:58.247] iteration 68: loss: 0.936971, mix_dice: 1.549313, mix_ce: 0.324629
[11:44:58.380] iteration 69: loss: 0.857425, mix_dice: 1.530115, mix_ce: 0.184734
[11:44:58.497] iteration 70: loss: 0.912475, mix_dice: 1.515163, mix_ce: 0.309787
[11:44:58.628] iteration 71: loss: 0.900579, mix_dice: 1.605586, mix_ce: 0.195572
[11:44:59.041] iteration 72: loss: 0.904898, mix_dice: 1.564295, mix_ce: 0.245501
[11:44:59.190] iteration 73: loss: 0.915608, mix_dice: 1.582861, mix_ce: 0.248354
[11:44:59.290] iteration 74: loss: 0.877872, mix_dice: 1.496457, mix_ce: 0.259287
[11:44:59.399] iteration 75: loss: 0.940021, mix_dice: 1.485730, mix_ce: 0.394313
[11:44:59.617] iteration 76: loss: 0.942581, mix_dice: 1.563130, mix_ce: 0.322031
[11:44:59.667] iteration 77: loss: 0.892944, mix_dice: 1.526969, mix_ce: 0.258919
[11:44:59.717] iteration 78: loss: 0.933285, mix_dice: 1.612204, mix_ce: 0.254366
[11:44:59.788] iteration 79: loss: 0.934678, mix_dice: 1.462969, mix_ce: 0.406388
[11:45:00.325] iteration 80: loss: 0.910353, mix_dice: 1.556249, mix_ce: 0.264458
[11:45:00.380] iteration 81: loss: 0.901784, mix_dice: 1.469046, mix_ce: 0.334522
[11:45:00.432] iteration 82: loss: 0.858599, mix_dice: 1.433403, mix_ce: 0.283794
[11:45:00.484] iteration 83: loss: 0.880607, mix_dice: 1.499200, mix_ce: 0.262015
[11:45:01.038] iteration 84: loss: 0.848857, mix_dice: 1.467920, mix_ce: 0.229794
[11:45:02.136] iteration 85: loss: 0.940008, mix_dice: 1.506357, mix_ce: 0.373660
[11:45:02.236] iteration 86: loss: 0.884454, mix_dice: 1.573233, mix_ce: 0.195676
[11:45:02.373] iteration 87: loss: 0.865863, mix_dice: 1.464721, mix_ce: 0.267005
[11:45:02.544] iteration 88: loss: 0.887789, mix_dice: 1.490380, mix_ce: 0.285197
[11:45:02.702] iteration 89: loss: 0.908443, mix_dice: 1.547998, mix_ce: 0.268888
[11:45:02.788] iteration 90: loss: 0.969622, mix_dice: 1.524820, mix_ce: 0.414423
[11:45:02.854] iteration 91: loss: 0.891997, mix_dice: 1.495867, mix_ce: 0.288127
[11:45:02.935] iteration 92: loss: 0.870153, mix_dice: 1.457638, mix_ce: 0.282668
[11:45:03.440] iteration 93: loss: 0.902476, mix_dice: 1.585790, mix_ce: 0.219162
[11:45:03.532] iteration 94: loss: 0.912959, mix_dice: 1.538036, mix_ce: 0.287883
[11:45:03.611] iteration 95: loss: 0.838764, mix_dice: 1.417584, mix_ce: 0.259943
[11:45:03.724] iteration 96: loss: 0.838860, mix_dice: 1.510876, mix_ce: 0.166843
[11:45:04.074] iteration 97: loss: 0.861677, mix_dice: 1.482639, mix_ce: 0.240714
[11:45:04.153] iteration 98: loss: 0.923349, mix_dice: 1.589903, mix_ce: 0.256795
[11:45:04.217] iteration 99: loss: 0.913462, mix_dice: 1.570351, mix_ce: 0.256574
[11:45:04.277] iteration 100: loss: 0.821780, mix_dice: 1.499744, mix_ce: 0.143816
[11:45:04.669] iteration 101: loss: 0.891269, mix_dice: 1.506861, mix_ce: 0.275676
[11:45:04.748] iteration 102: loss: 0.917681, mix_dice: 1.586584, mix_ce: 0.248778
[11:45:04.800] iteration 103: loss: 0.844966, mix_dice: 1.522302, mix_ce: 0.167630
[11:45:04.850] iteration 104: loss: 0.943539, mix_dice: 1.631006, mix_ce: 0.256072
[11:45:05.321] iteration 105: loss: 0.871979, mix_dice: 1.531359, mix_ce: 0.212599
[11:45:06.417] iteration 106: loss: 0.898149, mix_dice: 1.478508, mix_ce: 0.317790
[11:45:06.468] iteration 107: loss: 0.954079, mix_dice: 1.614713, mix_ce: 0.293444
[11:45:06.518] iteration 108: loss: 0.873752, mix_dice: 1.477071, mix_ce: 0.270434
[11:45:06.567] iteration 109: loss: 0.874888, mix_dice: 1.602768, mix_ce: 0.147009
[11:45:07.003] iteration 110: loss: 0.883909, mix_dice: 1.490978, mix_ce: 0.276840
[11:45:07.144] iteration 111: loss: 0.878365, mix_dice: 1.575336, mix_ce: 0.181394
[11:45:07.345] iteration 112: loss: 0.879968, mix_dice: 1.502064, mix_ce: 0.257872
[11:45:07.475] iteration 113: loss: 0.853811, mix_dice: 1.516999, mix_ce: 0.190624
[11:45:07.727] iteration 114: loss: 0.871109, mix_dice: 1.522307, mix_ce: 0.219911
[11:45:07.861] iteration 115: loss: 0.850998, mix_dice: 1.536303, mix_ce: 0.165693
[11:45:07.920] iteration 116: loss: 0.898332, mix_dice: 1.555836, mix_ce: 0.240827
[11:45:07.970] iteration 117: loss: 0.860984, mix_dice: 1.441890, mix_ce: 0.280078
[11:45:08.531] iteration 118: loss: 0.936228, mix_dice: 1.553593, mix_ce: 0.318862
[11:45:08.699] iteration 119: loss: 0.898569, mix_dice: 1.483803, mix_ce: 0.313335
[11:45:08.751] iteration 120: loss: 0.893768, mix_dice: 1.511597, mix_ce: 0.275939
[11:45:08.857] iteration 121: loss: 0.952587, mix_dice: 1.555706, mix_ce: 0.349469
[11:45:09.051] iteration 122: loss: 0.916037, mix_dice: 1.645868, mix_ce: 0.186206
[11:45:09.225] iteration 123: loss: 0.962892, mix_dice: 1.609749, mix_ce: 0.316035
[11:45:09.379] iteration 124: loss: 0.846817, mix_dice: 1.468738, mix_ce: 0.224895
[11:45:09.509] iteration 125: loss: 0.907964, mix_dice: 1.505576, mix_ce: 0.310352
[11:45:09.623] iteration 126: loss: 0.871741, mix_dice: 1.511834, mix_ce: 0.231649
[11:45:10.655] iteration 127: loss: 0.854875, mix_dice: 1.506720, mix_ce: 0.203029
[11:45:10.706] iteration 128: loss: 0.860301, mix_dice: 1.498069, mix_ce: 0.222532
[11:45:10.818] iteration 129: loss: 0.909214, mix_dice: 1.616654, mix_ce: 0.201773
[11:45:10.943] iteration 130: loss: 0.866041, mix_dice: 1.482997, mix_ce: 0.249085
[11:45:11.621] iteration 131: loss: 0.849354, mix_dice: 1.458996, mix_ce: 0.239711
[11:45:11.758] iteration 132: loss: 0.866356, mix_dice: 1.554672, mix_ce: 0.178040
[11:45:11.819] iteration 133: loss: 0.864181, mix_dice: 1.474938, mix_ce: 0.253424
[11:45:11.878] iteration 134: loss: 0.934282, mix_dice: 1.539095, mix_ce: 0.329470
[11:45:12.249] iteration 135: loss: 0.892848, mix_dice: 1.548767, mix_ce: 0.236929
[11:45:12.669] iteration 136: loss: 0.925301, mix_dice: 1.591518, mix_ce: 0.259084
[11:45:12.903] iteration 137: loss: 0.862356, mix_dice: 1.468360, mix_ce: 0.256352
[11:45:12.979] iteration 138: loss: 0.889333, mix_dice: 1.608597, mix_ce: 0.170068
[11:45:13.054] iteration 139: loss: 0.898101, mix_dice: 1.522769, mix_ce: 0.273433
[11:45:13.113] iteration 140: loss: 0.905636, mix_dice: 1.515787, mix_ce: 0.295485
[11:45:13.162] iteration 141: loss: 0.946310, mix_dice: 1.732557, mix_ce: 0.160063
[11:45:13.211] iteration 142: loss: 0.869013, mix_dice: 1.493171, mix_ce: 0.244855
[11:45:13.261] iteration 143: loss: 0.955156, mix_dice: 1.617499, mix_ce: 0.292813
[11:45:13.362] iteration 144: loss: 0.850995, mix_dice: 1.459566, mix_ce: 0.242424
[11:45:13.549] iteration 145: loss: 0.896818, mix_dice: 1.572170, mix_ce: 0.221465
[11:45:13.647] iteration 146: loss: 0.875766, mix_dice: 1.478134, mix_ce: 0.273398
[11:45:13.979] iteration 147: loss: 0.853207, mix_dice: 1.516675, mix_ce: 0.189740
[11:45:15.328] iteration 148: loss: 0.846848, mix_dice: 1.459303, mix_ce: 0.234394
[11:45:15.572] iteration 149: loss: 0.866504, mix_dice: 1.433291, mix_ce: 0.299717
[11:45:15.719] iteration 150: loss: 0.887559, mix_dice: 1.473935, mix_ce: 0.301182
[11:45:15.954] iteration 151: loss: 0.941372, mix_dice: 1.665689, mix_ce: 0.217054
[11:45:16.167] iteration 152: loss: 0.870405, mix_dice: 1.472248, mix_ce: 0.268561
[11:45:16.341] iteration 153: loss: 0.881875, mix_dice: 1.482998, mix_ce: 0.280752
[11:45:16.438] iteration 154: loss: 0.905293, mix_dice: 1.583042, mix_ce: 0.227543
[11:45:16.497] iteration 155: loss: 0.880218, mix_dice: 1.553879, mix_ce: 0.206558
[11:45:16.550] iteration 156: loss: 0.888987, mix_dice: 1.584854, mix_ce: 0.193119
[11:45:16.619] iteration 157: loss: 0.863289, mix_dice: 1.500878, mix_ce: 0.225700
[11:45:16.708] iteration 158: loss: 0.902939, mix_dice: 1.468664, mix_ce: 0.337214
[11:45:16.878] iteration 159: loss: 0.911561, mix_dice: 1.509216, mix_ce: 0.313905
[11:45:17.050] iteration 160: loss: 0.908285, mix_dice: 1.527659, mix_ce: 0.288910
[11:45:17.171] iteration 161: loss: 0.901287, mix_dice: 1.505789, mix_ce: 0.296786
[11:45:17.221] iteration 162: loss: 0.864299, mix_dice: 1.521433, mix_ce: 0.207165
[11:45:17.272] iteration 163: loss: 0.861182, mix_dice: 1.481329, mix_ce: 0.241035
[11:45:17.551] iteration 164: loss: 0.853626, mix_dice: 1.427442, mix_ce: 0.279810
[11:45:17.648] iteration 165: loss: 0.835650, mix_dice: 1.484337, mix_ce: 0.186962
[11:45:17.887] iteration 166: loss: 0.823147, mix_dice: 1.444482, mix_ce: 0.201811
[11:45:18.077] iteration 167: loss: 0.816109, mix_dice: 1.448557, mix_ce: 0.183660
[11:45:18.419] iteration 168: loss: 0.904263, mix_dice: 1.641062, mix_ce: 0.167463
[11:45:19.875] iteration 169: loss: 0.931432, mix_dice: 1.545050, mix_ce: 0.317813
[11:45:20.158] iteration 170: loss: 0.859493, mix_dice: 1.459625, mix_ce: 0.259361
[11:45:20.233] iteration 171: loss: 0.861460, mix_dice: 1.510001, mix_ce: 0.212920
[11:45:20.289] iteration 172: loss: 0.908066, mix_dice: 1.642395, mix_ce: 0.173738
[11:45:20.345] iteration 173: loss: 0.872686, mix_dice: 1.524952, mix_ce: 0.220419
[11:45:20.402] iteration 174: loss: 0.896211, mix_dice: 1.637151, mix_ce: 0.155272
[11:45:20.620] iteration 175: loss: 0.965807, mix_dice: 1.514600, mix_ce: 0.417013
[11:45:20.673] iteration 176: loss: 0.870149, mix_dice: 1.463293, mix_ce: 0.277005
[11:45:20.979] iteration 177: loss: 0.830228, mix_dice: 1.478850, mix_ce: 0.181606
[11:45:21.237] iteration 178: loss: 0.834948, mix_dice: 1.477337, mix_ce: 0.192560
[11:45:21.466] iteration 179: loss: 0.893574, mix_dice: 1.531176, mix_ce: 0.255973
[11:45:21.689] iteration 180: loss: 0.835008, mix_dice: 1.467945, mix_ce: 0.202072
[11:45:21.893] iteration 181: loss: 0.915295, mix_dice: 1.507501, mix_ce: 0.323088
[11:45:22.068] iteration 182: loss: 0.853157, mix_dice: 1.507330, mix_ce: 0.198984
[11:45:22.226] iteration 183: loss: 0.904302, mix_dice: 1.603128, mix_ce: 0.205475
[11:45:22.289] iteration 184: loss: 0.802115, mix_dice: 1.466429, mix_ce: 0.137800
[11:45:22.345] iteration 185: loss: 0.897786, mix_dice: 1.609783, mix_ce: 0.185789
[11:45:22.403] iteration 186: loss: 0.847629, mix_dice: 1.511197, mix_ce: 0.184061
[11:45:22.459] iteration 187: loss: 0.870717, mix_dice: 1.452506, mix_ce: 0.288927
[11:45:22.509] iteration 188: loss: 0.899102, mix_dice: 1.486652, mix_ce: 0.311553
[11:45:22.691] iteration 189: loss: 0.926535, mix_dice: 1.636006, mix_ce: 0.217065
[11:45:23.726] iteration 190: loss: 0.825853, mix_dice: 1.479225, mix_ce: 0.172480
[11:45:23.800] iteration 191: loss: 0.887614, mix_dice: 1.478714, mix_ce: 0.296515
[11:45:23.861] iteration 192: loss: 0.898760, mix_dice: 1.561737, mix_ce: 0.235784
[11:45:23.998] iteration 193: loss: 0.867106, mix_dice: 1.572247, mix_ce: 0.161965
[11:45:24.261] iteration 194: loss: 0.875527, mix_dice: 1.565860, mix_ce: 0.185194
[11:45:24.312] iteration 195: loss: 0.857974, mix_dice: 1.504107, mix_ce: 0.211842
[11:45:24.362] iteration 196: loss: 0.850678, mix_dice: 1.498360, mix_ce: 0.202995
[11:45:24.415] iteration 197: loss: 0.896070, mix_dice: 1.527572, mix_ce: 0.264568
[11:45:25.032] iteration 198: loss: 0.878771, mix_dice: 1.516062, mix_ce: 0.241481
[11:45:25.139] iteration 199: loss: 0.880199, mix_dice: 1.504553, mix_ce: 0.255845
[11:45:25.247] iteration 200: loss: 0.889437, mix_dice: 1.536674, mix_ce: 0.242200
[11:45:25.372] iteration 201: loss: 0.928115, mix_dice: 1.705550, mix_ce: 0.150681
[11:45:25.677] iteration 202: loss: 0.891765, mix_dice: 1.590430, mix_ce: 0.193101
[11:45:25.771] iteration 203: loss: 0.881843, mix_dice: 1.558813, mix_ce: 0.204874
[11:45:25.868] iteration 204: loss: 0.853672, mix_dice: 1.482059, mix_ce: 0.225285
[11:45:25.949] iteration 205: loss: 0.828194, mix_dice: 1.513543, mix_ce: 0.142845
[11:45:26.252] iteration 206: loss: 0.854676, mix_dice: 1.476097, mix_ce: 0.233255
[11:45:26.304] iteration 207: loss: 0.894753, mix_dice: 1.505804, mix_ce: 0.283702
[11:45:26.357] iteration 208: loss: 0.885433, mix_dice: 1.530949, mix_ce: 0.239917
[11:45:26.412] iteration 209: loss: 0.913471, mix_dice: 1.565721, mix_ce: 0.261220
[11:45:26.895] iteration 210: loss: 0.883401, mix_dice: 1.644080, mix_ce: 0.122722
[11:45:27.782] iteration 211: loss: 0.865589, mix_dice: 1.495533, mix_ce: 0.235645
[11:45:27.834] iteration 212: loss: 0.856509, mix_dice: 1.526023, mix_ce: 0.186995
[11:45:27.887] iteration 213: loss: 0.906943, mix_dice: 1.603984, mix_ce: 0.209902
[11:45:27.937] iteration 214: loss: 0.890628, mix_dice: 1.562531, mix_ce: 0.218725
[11:45:28.356] iteration 215: loss: 0.906525, mix_dice: 1.521170, mix_ce: 0.291880
[11:45:28.493] iteration 216: loss: 0.816170, mix_dice: 1.448259, mix_ce: 0.184080
[11:45:28.555] iteration 217: loss: 0.820488, mix_dice: 1.457175, mix_ce: 0.183801
[11:45:28.620] iteration 218: loss: 0.862639, mix_dice: 1.521986, mix_ce: 0.203291
[11:45:29.108] iteration 219: loss: 0.947167, mix_dice: 1.645633, mix_ce: 0.248701
[11:45:29.179] iteration 220: loss: 0.946720, mix_dice: 1.531243, mix_ce: 0.362197
[11:45:29.246] iteration 221: loss: 0.787134, mix_dice: 1.406926, mix_ce: 0.167342
[11:45:29.298] iteration 222: loss: 0.908638, mix_dice: 1.514928, mix_ce: 0.302349
[11:45:29.764] iteration 223: loss: 0.876958, mix_dice: 1.499679, mix_ce: 0.254238
[11:45:29.817] iteration 224: loss: 0.833393, mix_dice: 1.408326, mix_ce: 0.258461
[11:45:29.867] iteration 225: loss: 0.891245, mix_dice: 1.596618, mix_ce: 0.185871
[11:45:29.918] iteration 226: loss: 0.853666, mix_dice: 1.515846, mix_ce: 0.191487
[11:45:30.438] iteration 227: loss: 0.874230, mix_dice: 1.487288, mix_ce: 0.261172
[11:45:30.510] iteration 228: loss: 0.848618, mix_dice: 1.483385, mix_ce: 0.213851
[11:45:30.571] iteration 229: loss: 0.840215, mix_dice: 1.465430, mix_ce: 0.215000
[11:45:30.637] iteration 230: loss: 0.895365, mix_dice: 1.595370, mix_ce: 0.195360
[11:45:31.046] iteration 231: loss: 0.874494, mix_dice: 1.525334, mix_ce: 0.223654
[11:45:32.052] iteration 232: loss: 0.877322, mix_dice: 1.667788, mix_ce: 0.086855
[11:45:32.103] iteration 233: loss: 0.872449, mix_dice: 1.570961, mix_ce: 0.173937
[11:45:32.155] iteration 234: loss: 0.926040, mix_dice: 1.596134, mix_ce: 0.255945
[11:45:32.215] iteration 235: loss: 0.944647, mix_dice: 1.616594, mix_ce: 0.272700
[11:45:32.618] iteration 236: loss: 0.854653, mix_dice: 1.540981, mix_ce: 0.168325
[11:45:32.713] iteration 237: loss: 0.807666, mix_dice: 1.478605, mix_ce: 0.136727
[11:45:32.804] iteration 238: loss: 0.886045, mix_dice: 1.483985, mix_ce: 0.288106
[11:45:32.880] iteration 239: loss: 0.961240, mix_dice: 1.595852, mix_ce: 0.326628
[11:45:33.273] iteration 240: loss: 0.933929, mix_dice: 1.557882, mix_ce: 0.309977
[11:45:33.325] iteration 241: loss: 0.892959, mix_dice: 1.558552, mix_ce: 0.227366
[11:45:33.380] iteration 242: loss: 0.929636, mix_dice: 1.577238, mix_ce: 0.282034
[11:45:33.440] iteration 243: loss: 0.871203, mix_dice: 1.511294, mix_ce: 0.231112
[11:45:33.914] iteration 244: loss: 0.881519, mix_dice: 1.593809, mix_ce: 0.169228
[11:45:33.974] iteration 245: loss: 0.861240, mix_dice: 1.487078, mix_ce: 0.235402
[11:45:34.030] iteration 246: loss: 0.817689, mix_dice: 1.428635, mix_ce: 0.206742
[11:45:34.089] iteration 247: loss: 0.853783, mix_dice: 1.537633, mix_ce: 0.169933
[11:45:34.683] iteration 248: loss: 0.862902, mix_dice: 1.457875, mix_ce: 0.267928
[11:45:34.874] iteration 249: loss: 0.893512, mix_dice: 1.599425, mix_ce: 0.187600
[11:45:35.004] iteration 250: loss: 0.891196, mix_dice: 1.527066, mix_ce: 0.255325
[11:45:35.093] iteration 251: loss: 0.859070, mix_dice: 1.474087, mix_ce: 0.244053
[11:45:35.212] iteration 252: loss: 0.813998, mix_dice: 1.440144, mix_ce: 0.187853
[11:45:36.302] iteration 253: loss: 0.850784, mix_dice: 1.575881, mix_ce: 0.125686
[11:45:36.356] iteration 254: loss: 0.858768, mix_dice: 1.464633, mix_ce: 0.252903
[11:45:36.410] iteration 255: loss: 0.892901, mix_dice: 1.635186, mix_ce: 0.150616
[11:45:36.462] iteration 256: loss: 0.907008, mix_dice: 1.719518, mix_ce: 0.094498
[11:45:36.818] iteration 257: loss: 0.927400, mix_dice: 1.561613, mix_ce: 0.293187
[11:45:36.943] iteration 258: loss: 0.908276, mix_dice: 1.703979, mix_ce: 0.112572
[11:45:37.019] iteration 259: loss: 0.833730, mix_dice: 1.530478, mix_ce: 0.136981
[11:45:37.098] iteration 260: loss: 0.851078, mix_dice: 1.537283, mix_ce: 0.164873
[11:45:37.509] iteration 261: loss: 0.832811, mix_dice: 1.479467, mix_ce: 0.186156
[11:45:37.611] iteration 262: loss: 0.884798, mix_dice: 1.484656, mix_ce: 0.284941
[11:45:37.683] iteration 263: loss: 0.884737, mix_dice: 1.489437, mix_ce: 0.280037
[11:45:37.758] iteration 264: loss: 0.839888, mix_dice: 1.478959, mix_ce: 0.200817
[11:45:38.186] iteration 265: loss: 0.823441, mix_dice: 1.423733, mix_ce: 0.223149
[11:45:38.235] iteration 266: loss: 0.839957, mix_dice: 1.464050, mix_ce: 0.215864
[11:45:38.285] iteration 267: loss: 0.836032, mix_dice: 1.428715, mix_ce: 0.243348
[11:45:38.336] iteration 268: loss: 0.843778, mix_dice: 1.488396, mix_ce: 0.199160
[11:45:38.865] iteration 269: loss: 0.899821, mix_dice: 1.473255, mix_ce: 0.326386
[11:45:38.930] iteration 270: loss: 0.875988, mix_dice: 1.479186, mix_ce: 0.272790
[11:45:38.998] iteration 271: loss: 0.837909, mix_dice: 1.436195, mix_ce: 0.239622
[11:45:39.060] iteration 272: loss: 0.936193, mix_dice: 1.534327, mix_ce: 0.338059
[11:45:39.533] iteration 273: loss: 0.925858, mix_dice: 1.498070, mix_ce: 0.353646
[11:45:40.539] iteration 274: loss: 0.889114, mix_dice: 1.496930, mix_ce: 0.281297
[11:45:40.603] iteration 275: loss: 0.892173, mix_dice: 1.599838, mix_ce: 0.184507
[11:45:40.658] iteration 276: loss: 0.866429, mix_dice: 1.466174, mix_ce: 0.266684
[11:45:40.712] iteration 277: loss: 0.863236, mix_dice: 1.467609, mix_ce: 0.258862
[11:45:41.181] iteration 278: loss: 0.837101, mix_dice: 1.464658, mix_ce: 0.209543
[11:45:41.236] iteration 279: loss: 0.890013, mix_dice: 1.528254, mix_ce: 0.251773
[11:45:41.291] iteration 280: loss: 0.853200, mix_dice: 1.547004, mix_ce: 0.159396
[11:45:41.346] iteration 281: loss: 0.880954, mix_dice: 1.485623, mix_ce: 0.276285
[11:45:41.961] iteration 282: loss: 0.868941, mix_dice: 1.492004, mix_ce: 0.245877
[11:45:42.020] iteration 283: loss: 0.816644, mix_dice: 1.416191, mix_ce: 0.217098
[11:45:42.092] iteration 284: loss: 0.835921, mix_dice: 1.515573, mix_ce: 0.156270
[11:45:42.158] iteration 285: loss: 0.872467, mix_dice: 1.528312, mix_ce: 0.216623
[11:45:42.637] iteration 286: loss: 0.907616, mix_dice: 1.478194, mix_ce: 0.337039
[11:45:42.688] iteration 287: loss: 0.887579, mix_dice: 1.488295, mix_ce: 0.286864
[11:45:42.740] iteration 288: loss: 0.791294, mix_dice: 1.426899, mix_ce: 0.155689
[11:45:42.792] iteration 289: loss: 0.878229, mix_dice: 1.563307, mix_ce: 0.193150
[11:45:43.365] iteration 290: loss: 0.870196, mix_dice: 1.585895, mix_ce: 0.154497
[11:45:43.439] iteration 291: loss: 0.929014, mix_dice: 1.640626, mix_ce: 0.217402
[11:45:43.508] iteration 292: loss: 0.850661, mix_dice: 1.533590, mix_ce: 0.167732
[11:45:43.565] iteration 293: loss: 0.815867, mix_dice: 1.403516, mix_ce: 0.228217
[11:45:44.014] iteration 294: loss: 0.866850, mix_dice: 1.523564, mix_ce: 0.210137
[11:45:44.969] iteration 295: loss: 0.851306, mix_dice: 1.463885, mix_ce: 0.238726
[11:45:45.065] iteration 296: loss: 0.865517, mix_dice: 1.539313, mix_ce: 0.191721
[11:45:45.118] iteration 297: loss: 0.844688, mix_dice: 1.477976, mix_ce: 0.211400
[11:45:45.178] iteration 298: loss: 0.843507, mix_dice: 1.451902, mix_ce: 0.235111
[11:45:45.530] iteration 299: loss: 0.872373, mix_dice: 1.516144, mix_ce: 0.228601
[11:45:45.712] iteration 300: loss: 0.797609, mix_dice: 1.357567, mix_ce: 0.237651
[11:45:45.765] iteration 301: loss: 0.831741, mix_dice: 1.443927, mix_ce: 0.219555
[11:45:45.816] iteration 302: loss: 0.876655, mix_dice: 1.619583, mix_ce: 0.133726
[11:45:46.219] iteration 303: loss: 0.817373, mix_dice: 1.467581, mix_ce: 0.167166
[11:45:46.396] iteration 304: loss: 0.833142, mix_dice: 1.407172, mix_ce: 0.259111
[11:45:46.522] iteration 305: loss: 0.834198, mix_dice: 1.400796, mix_ce: 0.267600
[11:45:46.626] iteration 306: loss: 0.841481, mix_dice: 1.436373, mix_ce: 0.246588
[11:45:46.892] iteration 307: loss: 0.863470, mix_dice: 1.531949, mix_ce: 0.194991
[11:45:46.984] iteration 308: loss: 0.840440, mix_dice: 1.537146, mix_ce: 0.143733
[11:45:47.137] iteration 309: loss: 0.843814, mix_dice: 1.544698, mix_ce: 0.142930
[11:45:47.324] iteration 310: loss: 0.820496, mix_dice: 1.483661, mix_ce: 0.157331
[11:45:47.571] iteration 311: loss: 0.831610, mix_dice: 1.443749, mix_ce: 0.219472
[11:45:47.686] iteration 312: loss: 0.850943, mix_dice: 1.512798, mix_ce: 0.189087
[11:45:47.777] iteration 313: loss: 0.894974, mix_dice: 1.622059, mix_ce: 0.167889
[11:45:47.869] iteration 314: loss: 0.858872, mix_dice: 1.455911, mix_ce: 0.261834
[11:45:48.117] iteration 315: loss: 0.873257, mix_dice: 1.465077, mix_ce: 0.281436
[11:45:49.251] iteration 316: loss: 0.830970, mix_dice: 1.433908, mix_ce: 0.228032
[11:45:49.392] iteration 317: loss: 0.899923, mix_dice: 1.578393, mix_ce: 0.221453
[11:45:49.531] iteration 318: loss: 0.835881, mix_dice: 1.448384, mix_ce: 0.223378
[11:45:49.672] iteration 319: loss: 0.893434, mix_dice: 1.547582, mix_ce: 0.239286
[11:45:49.831] iteration 320: loss: 0.887969, mix_dice: 1.608787, mix_ce: 0.167151
[11:45:49.942] iteration 321: loss: 0.808933, mix_dice: 1.479037, mix_ce: 0.138830
[11:45:49.996] iteration 322: loss: 0.873833, mix_dice: 1.516837, mix_ce: 0.230829
[11:45:50.060] iteration 323: loss: 0.844133, mix_dice: 1.495984, mix_ce: 0.192282
[11:45:50.300] iteration 324: loss: 0.797831, mix_dice: 1.439739, mix_ce: 0.155922
[11:45:50.418] iteration 325: loss: 0.893178, mix_dice: 1.514374, mix_ce: 0.271981
[11:45:50.549] iteration 326: loss: 0.883887, mix_dice: 1.552151, mix_ce: 0.215623
[11:45:50.674] iteration 327: loss: 0.954093, mix_dice: 1.535466, mix_ce: 0.372720
[11:45:50.995] iteration 328: loss: 0.849802, mix_dice: 1.451027, mix_ce: 0.248576
[11:45:51.099] iteration 329: loss: 0.830725, mix_dice: 1.395902, mix_ce: 0.265548
[11:45:51.211] iteration 330: loss: 0.857764, mix_dice: 1.499582, mix_ce: 0.215945
[11:45:51.320] iteration 331: loss: 0.859952, mix_dice: 1.491901, mix_ce: 0.228003
[11:45:51.674] iteration 332: loss: 0.902278, mix_dice: 1.548347, mix_ce: 0.256210
[11:45:51.759] iteration 333: loss: 0.865936, mix_dice: 1.448089, mix_ce: 0.283782
[11:45:51.819] iteration 334: loss: 0.853644, mix_dice: 1.485778, mix_ce: 0.221510
[11:45:51.868] iteration 335: loss: 0.803018, mix_dice: 1.416173, mix_ce: 0.189864
[11:45:52.244] iteration 336: loss: 0.900700, mix_dice: 1.514993, mix_ce: 0.286406
[11:45:53.123] iteration 337: loss: 0.782813, mix_dice: 1.398397, mix_ce: 0.167229
[11:45:53.173] iteration 338: loss: 0.866734, mix_dice: 1.606647, mix_ce: 0.126820
[11:45:53.222] iteration 339: loss: 0.830428, mix_dice: 1.419981, mix_ce: 0.240875
[11:45:53.274] iteration 340: loss: 0.876381, mix_dice: 1.532980, mix_ce: 0.219783
[11:45:53.694] iteration 341: loss: 0.823992, mix_dice: 1.465885, mix_ce: 0.182099
[11:45:53.753] iteration 342: loss: 0.868418, mix_dice: 1.541098, mix_ce: 0.195737
[11:45:53.813] iteration 343: loss: 0.927481, mix_dice: 1.479110, mix_ce: 0.375852
[11:45:53.920] iteration 344: loss: 0.856866, mix_dice: 1.566919, mix_ce: 0.146814
[11:45:54.399] iteration 345: loss: 0.790949, mix_dice: 1.395547, mix_ce: 0.186350
[11:45:54.468] iteration 346: loss: 0.879835, mix_dice: 1.506639, mix_ce: 0.253030
[11:45:54.537] iteration 347: loss: 0.805537, mix_dice: 1.416419, mix_ce: 0.194656
[11:45:54.623] iteration 348: loss: 0.804606, mix_dice: 1.392188, mix_ce: 0.217023
[11:45:55.160] iteration 349: loss: 0.923599, mix_dice: 1.668744, mix_ce: 0.178453
[11:45:55.287] iteration 350: loss: 0.815643, mix_dice: 1.400310, mix_ce: 0.230977
[11:45:55.362] iteration 351: loss: 0.841404, mix_dice: 1.505268, mix_ce: 0.177540
[11:45:55.419] iteration 352: loss: 0.846434, mix_dice: 1.575663, mix_ce: 0.117204
[11:45:55.761] iteration 353: loss: 0.857413, mix_dice: 1.478096, mix_ce: 0.236731
[11:45:55.862] iteration 354: loss: 0.844586, mix_dice: 1.425076, mix_ce: 0.264097
[11:45:55.961] iteration 355: loss: 0.823401, mix_dice: 1.502834, mix_ce: 0.143969
[11:45:56.086] iteration 356: loss: 0.784256, mix_dice: 1.428176, mix_ce: 0.140336
[11:45:56.312] iteration 357: loss: 0.854903, mix_dice: 1.563178, mix_ce: 0.146627
[11:45:57.430] iteration 358: loss: 0.837248, mix_dice: 1.423385, mix_ce: 0.251111
[11:45:57.557] iteration 359: loss: 0.803065, mix_dice: 1.443560, mix_ce: 0.162571
[11:45:57.649] iteration 360: loss: 0.819576, mix_dice: 1.444532, mix_ce: 0.194621
[11:45:57.774] iteration 361: loss: 0.840233, mix_dice: 1.410823, mix_ce: 0.269644
[11:45:57.947] iteration 362: loss: 0.796001, mix_dice: 1.469439, mix_ce: 0.122564
[11:45:58.019] iteration 363: loss: 0.803157, mix_dice: 1.418509, mix_ce: 0.187805
[11:45:58.124] iteration 364: loss: 0.813911, mix_dice: 1.440879, mix_ce: 0.186944
[11:45:58.221] iteration 365: loss: 0.807175, mix_dice: 1.450549, mix_ce: 0.163801
[11:45:58.595] iteration 366: loss: 0.836567, mix_dice: 1.524492, mix_ce: 0.148643
[11:45:58.649] iteration 367: loss: 0.859066, mix_dice: 1.501264, mix_ce: 0.216867
[11:45:58.714] iteration 368: loss: 0.798072, mix_dice: 1.449382, mix_ce: 0.146763
[11:45:58.777] iteration 369: loss: 0.867449, mix_dice: 1.535030, mix_ce: 0.199869
[11:45:59.343] iteration 370: loss: 0.845404, mix_dice: 1.494644, mix_ce: 0.196163
[11:45:59.421] iteration 371: loss: 0.874075, mix_dice: 1.544049, mix_ce: 0.204100
[11:45:59.528] iteration 372: loss: 0.852051, mix_dice: 1.491049, mix_ce: 0.213053
[11:45:59.649] iteration 373: loss: 0.813218, mix_dice: 1.458901, mix_ce: 0.167536
[11:46:00.123] iteration 374: loss: 0.794021, mix_dice: 1.382620, mix_ce: 0.205422
[11:46:00.481] iteration 375: loss: 0.800790, mix_dice: 1.415616, mix_ce: 0.185964
[11:46:00.742] iteration 376: loss: 0.895996, mix_dice: 1.670636, mix_ce: 0.121355
[11:46:00.987] iteration 377: loss: 0.875972, mix_dice: 1.545793, mix_ce: 0.206152
[11:46:01.158] iteration 378: loss: 0.879999, mix_dice: 1.488351, mix_ce: 0.271647
[11:46:02.647] iteration 379: loss: 0.865094, mix_dice: 1.406355, mix_ce: 0.323833
[11:46:02.886] iteration 380: loss: 0.896059, mix_dice: 1.472838, mix_ce: 0.319281
[11:46:03.081] iteration 381: loss: 0.804553, mix_dice: 1.427676, mix_ce: 0.181430
[11:46:03.143] iteration 382: loss: 0.828017, mix_dice: 1.408818, mix_ce: 0.247217
[11:46:03.196] iteration 383: loss: 0.809960, mix_dice: 1.429783, mix_ce: 0.190137
[11:46:03.245] iteration 384: loss: 0.895166, mix_dice: 1.592367, mix_ce: 0.197966
[11:46:03.294] iteration 385: loss: 0.860442, mix_dice: 1.444264, mix_ce: 0.276620
[11:46:03.391] iteration 386: loss: 0.797374, mix_dice: 1.435423, mix_ce: 0.159325
[11:46:03.597] iteration 387: loss: 0.867345, mix_dice: 1.449462, mix_ce: 0.285227
[11:46:03.703] iteration 388: loss: 0.854029, mix_dice: 1.495757, mix_ce: 0.212301
[11:46:03.875] iteration 389: loss: 0.822916, mix_dice: 1.399355, mix_ce: 0.246476
[11:46:04.191] iteration 390: loss: 0.871870, mix_dice: 1.406583, mix_ce: 0.337158
[11:46:04.471] iteration 391: loss: 0.811987, mix_dice: 1.385203, mix_ce: 0.238770
[11:46:04.721] iteration 392: loss: 0.854611, mix_dice: 1.475276, mix_ce: 0.233945
[11:46:04.815] iteration 393: loss: 0.855294, mix_dice: 1.474872, mix_ce: 0.235716
[11:46:04.873] iteration 394: loss: 0.844785, mix_dice: 1.565979, mix_ce: 0.123591
[11:46:04.928] iteration 395: loss: 0.845965, mix_dice: 1.517040, mix_ce: 0.174890
[11:46:04.981] iteration 396: loss: 0.868147, mix_dice: 1.494039, mix_ce: 0.242255
[11:46:05.047] iteration 397: loss: 0.806312, mix_dice: 1.490433, mix_ce: 0.122190
[11:46:05.096] iteration 398: loss: 0.925135, mix_dice: 1.731137, mix_ce: 0.119134
[11:46:05.365] iteration 399: loss: 0.858287, mix_dice: 1.557032, mix_ce: 0.159542
[11:46:06.547] iteration 400: loss: 0.886305, mix_dice: 1.489685, mix_ce: 0.282925
[11:46:06.693] iteration 401: loss: 0.840515, mix_dice: 1.576135, mix_ce: 0.104896
[11:46:06.887] iteration 402: loss: 0.802888, mix_dice: 1.458534, mix_ce: 0.147241
[11:46:07.057] iteration 403: loss: 0.821916, mix_dice: 1.475933, mix_ce: 0.167899
[11:46:07.263] iteration 404: loss: 0.778911, mix_dice: 1.424819, mix_ce: 0.133002
[11:46:07.558] iteration 405: loss: 0.783012, mix_dice: 1.415920, mix_ce: 0.150104
[11:46:07.693] iteration 406: loss: 0.886916, mix_dice: 1.506257, mix_ce: 0.267575
[11:46:07.855] iteration 407: loss: 0.841641, mix_dice: 1.466634, mix_ce: 0.216647
[11:46:07.961] iteration 408: loss: 0.816752, mix_dice: 1.435654, mix_ce: 0.197850
[11:46:08.026] iteration 409: loss: 0.866129, mix_dice: 1.507597, mix_ce: 0.224660
[11:46:08.091] iteration 410: loss: 0.836196, mix_dice: 1.517711, mix_ce: 0.154681
[11:46:08.154] iteration 411: loss: 0.856540, mix_dice: 1.453544, mix_ce: 0.259536
[11:46:08.596] iteration 412: loss: 0.801376, mix_dice: 1.406404, mix_ce: 0.196348
[11:46:08.891] iteration 413: loss: 0.845381, mix_dice: 1.432081, mix_ce: 0.258681
[11:46:09.168] iteration 414: loss: 0.811040, mix_dice: 1.395034, mix_ce: 0.227046
[11:46:09.403] iteration 415: loss: 0.785871, mix_dice: 1.388793, mix_ce: 0.182949
[11:46:09.487] iteration 416: loss: 0.862398, mix_dice: 1.421561, mix_ce: 0.303235
[11:46:09.563] iteration 417: loss: 0.791268, mix_dice: 1.412761, mix_ce: 0.169774
[11:46:09.648] iteration 418: loss: 0.795876, mix_dice: 1.391042, mix_ce: 0.200710
[11:46:09.772] iteration 419: loss: 0.796029, mix_dice: 1.439344, mix_ce: 0.152714
[11:46:09.930] iteration 420: loss: 0.800563, mix_dice: 1.422762, mix_ce: 0.178363
[11:46:10.952] iteration 421: loss: 0.839072, mix_dice: 1.486542, mix_ce: 0.191602
[11:46:11.038] iteration 422: loss: 0.891213, mix_dice: 1.538641, mix_ce: 0.243785
[11:46:11.109] iteration 423: loss: 0.845279, mix_dice: 1.489290, mix_ce: 0.201268
[11:46:11.177] iteration 424: loss: 0.791443, mix_dice: 1.435046, mix_ce: 0.147839
[11:46:11.479] iteration 425: loss: 0.809714, mix_dice: 1.474466, mix_ce: 0.144963
[11:46:11.919] iteration 426: loss: 0.775631, mix_dice: 1.374617, mix_ce: 0.176644
[11:46:12.517] iteration 427: loss: 0.819466, mix_dice: 1.482958, mix_ce: 0.155973
[11:46:12.731] iteration 428: loss: 0.795165, mix_dice: 1.408240, mix_ce: 0.182090
[11:46:12.809] iteration 429: loss: 0.768775, mix_dice: 1.344833, mix_ce: 0.192717
[11:46:12.869] iteration 430: loss: 0.827477, mix_dice: 1.417643, mix_ce: 0.237311
[11:46:12.921] iteration 431: loss: 0.828931, mix_dice: 1.400926, mix_ce: 0.256935
[11:46:12.974] iteration 432: loss: 0.797019, mix_dice: 1.391039, mix_ce: 0.202998
[11:46:13.029] iteration 433: loss: 0.768818, mix_dice: 1.329138, mix_ce: 0.208498
[11:46:13.179] iteration 434: loss: 0.824150, mix_dice: 1.421832, mix_ce: 0.226467
[11:46:13.426] iteration 435: loss: 0.840348, mix_dice: 1.472557, mix_ce: 0.208139
[11:46:13.579] iteration 436: loss: 0.755315, mix_dice: 1.274688, mix_ce: 0.235943
[11:46:13.762] iteration 437: loss: 0.809489, mix_dice: 1.461199, mix_ce: 0.157780
[11:46:13.841] iteration 438: loss: 0.894859, mix_dice: 1.605586, mix_ce: 0.184131
[11:46:13.898] iteration 439: loss: 0.910581, mix_dice: 1.356340, mix_ce: 0.464823
[11:46:13.983] iteration 440: loss: 0.813682, mix_dice: 1.465561, mix_ce: 0.161803
[11:46:14.046] iteration 441: loss: 0.838155, mix_dice: 1.479736, mix_ce: 0.196574
[11:46:15.273] iteration 442: loss: 0.837182, mix_dice: 1.422853, mix_ce: 0.251512
[11:46:15.473] iteration 443: loss: 0.773801, mix_dice: 1.323355, mix_ce: 0.224246
[11:46:15.537] iteration 444: loss: 0.805549, mix_dice: 1.424773, mix_ce: 0.186325
[11:46:15.600] iteration 445: loss: 0.839844, mix_dice: 1.512848, mix_ce: 0.166840
[11:46:15.662] iteration 446: loss: 0.828842, mix_dice: 1.486455, mix_ce: 0.171228
[11:46:15.727] iteration 447: loss: 0.816894, mix_dice: 1.450958, mix_ce: 0.182831
[11:46:15.808] iteration 448: loss: 0.759084, mix_dice: 1.338548, mix_ce: 0.179619
[11:46:15.898] iteration 449: loss: 0.756825, mix_dice: 1.363290, mix_ce: 0.150361
[11:46:16.538] iteration 450: loss: 0.788577, mix_dice: 1.422222, mix_ce: 0.154931
[11:46:16.728] iteration 451: loss: 0.828457, mix_dice: 1.503474, mix_ce: 0.153440
[11:46:16.903] iteration 452: loss: 0.752195, mix_dice: 1.365675, mix_ce: 0.138716
[11:46:17.043] iteration 453: loss: 0.840584, mix_dice: 1.414145, mix_ce: 0.267024
[11:46:17.150] iteration 454: loss: 0.774218, mix_dice: 1.371849, mix_ce: 0.176587
[11:46:17.224] iteration 455: loss: 0.813412, mix_dice: 1.371310, mix_ce: 0.255513
[11:46:17.346] iteration 456: loss: 0.792377, mix_dice: 1.479394, mix_ce: 0.105360
[11:46:17.472] iteration 457: loss: 0.799195, mix_dice: 1.399541, mix_ce: 0.198848
[11:46:17.569] iteration 458: loss: 0.832478, mix_dice: 1.532949, mix_ce: 0.132006
[11:46:17.616] iteration 459: loss: 0.825786, mix_dice: 1.427633, mix_ce: 0.223939
[11:46:17.664] iteration 460: loss: 0.809010, mix_dice: 1.435384, mix_ce: 0.182635
[11:46:17.729] iteration 461: loss: 0.857970, mix_dice: 1.399394, mix_ce: 0.316547
[11:46:18.259] iteration 462: loss: 0.832475, mix_dice: 1.428471, mix_ce: 0.236478
[11:46:19.484] iteration 463: loss: 0.808027, mix_dice: 1.392045, mix_ce: 0.224009
[11:46:19.640] iteration 464: loss: 0.812499, mix_dice: 1.436816, mix_ce: 0.188182
[11:46:19.782] iteration 465: loss: 0.879698, mix_dice: 1.538090, mix_ce: 0.221306
[11:46:19.926] iteration 466: loss: 0.844393, mix_dice: 1.503935, mix_ce: 0.184851
[11:46:20.058] iteration 467: loss: 0.777937, mix_dice: 1.304135, mix_ce: 0.251739
[11:46:20.196] iteration 468: loss: 0.830936, mix_dice: 1.499569, mix_ce: 0.162304
[11:46:20.329] iteration 469: loss: 0.852737, mix_dice: 1.552342, mix_ce: 0.153131
[11:46:20.471] iteration 470: loss: 0.753601, mix_dice: 1.330838, mix_ce: 0.176364
[11:46:20.602] iteration 471: loss: 0.832918, mix_dice: 1.341626, mix_ce: 0.324211
[11:46:20.668] iteration 472: loss: 0.843712, mix_dice: 1.451797, mix_ce: 0.235626
[11:46:20.738] iteration 473: loss: 0.736657, mix_dice: 1.262243, mix_ce: 0.211071
[11:46:20.809] iteration 474: loss: 0.823853, mix_dice: 1.512900, mix_ce: 0.134806
[11:46:21.290] iteration 475: loss: 0.787517, mix_dice: 1.388917, mix_ce: 0.186116
[11:46:21.440] iteration 476: loss: 0.795673, mix_dice: 1.343580, mix_ce: 0.247766
[11:46:21.590] iteration 477: loss: 0.782080, mix_dice: 1.375238, mix_ce: 0.188923
[11:46:21.745] iteration 478: loss: 0.742226, mix_dice: 1.327065, mix_ce: 0.157387
[11:46:21.877] iteration 479: loss: 0.739470, mix_dice: 1.278024, mix_ce: 0.200916
[11:46:22.034] iteration 480: loss: 0.835846, mix_dice: 1.437015, mix_ce: 0.234677
[11:46:22.192] iteration 481: loss: 0.885321, mix_dice: 1.600221, mix_ce: 0.170421
[11:46:22.367] iteration 482: loss: 0.856062, mix_dice: 1.439782, mix_ce: 0.272342
[11:46:22.537] iteration 483: loss: 0.787956, mix_dice: 1.355693, mix_ce: 0.220220
[11:46:24.090] iteration 484: loss: 0.821300, mix_dice: 1.374150, mix_ce: 0.268450
[11:46:24.350] iteration 485: loss: 0.826680, mix_dice: 1.494677, mix_ce: 0.158683
[11:46:24.435] iteration 486: loss: 0.744399, mix_dice: 1.322216, mix_ce: 0.166583
[11:46:24.503] iteration 487: loss: 0.809454, mix_dice: 1.392171, mix_ce: 0.226737
[11:46:25.090] iteration 488: loss: 0.798492, mix_dice: 1.402636, mix_ce: 0.194348
[11:46:25.365] iteration 489: loss: 0.858033, mix_dice: 1.373800, mix_ce: 0.342267
[11:46:25.582] iteration 490: loss: 0.720588, mix_dice: 1.281935, mix_ce: 0.159242
[11:46:25.984] iteration 491: loss: 0.815305, mix_dice: 1.496662, mix_ce: 0.133948
[11:46:26.315] iteration 492: loss: 0.765708, mix_dice: 1.305658, mix_ce: 0.225757
[11:46:26.644] iteration 493: loss: 0.777247, mix_dice: 1.349929, mix_ce: 0.204565
[11:46:26.919] iteration 494: loss: 0.730224, mix_dice: 1.271369, mix_ce: 0.189078
[11:46:27.003] iteration 495: loss: 0.770894, mix_dice: 1.362251, mix_ce: 0.179538
[11:46:27.054] iteration 496: loss: 0.808371, mix_dice: 1.349565, mix_ce: 0.267177
[11:46:27.157] iteration 497: loss: 0.926281, mix_dice: 1.483544, mix_ce: 0.369018
[11:46:27.432] iteration 498: loss: 0.746842, mix_dice: 1.330899, mix_ce: 0.162785
[11:46:27.654] iteration 499: loss: 0.816519, mix_dice: 1.409682, mix_ce: 0.223356
[11:46:27.933] iteration 500: loss: 0.731642, mix_dice: 1.307248, mix_ce: 0.156035
[11:46:28.067] iteration 501: loss: 0.874033, mix_dice: 1.508927, mix_ce: 0.239138
[11:46:28.116] iteration 502: loss: 0.850140, mix_dice: 1.388577, mix_ce: 0.311703
[11:46:28.187] iteration 503: loss: 0.854882, mix_dice: 1.562923, mix_ce: 0.146842
[11:46:28.235] iteration 504: loss: 0.820846, mix_dice: 1.480600, mix_ce: 0.161092
[11:46:29.709] iteration 505: loss: 0.799650, mix_dice: 1.466018, mix_ce: 0.133282
[11:46:29.792] iteration 506: loss: 0.774365, mix_dice: 1.330236, mix_ce: 0.218494
[11:46:30.005] iteration 507: loss: 0.803057, mix_dice: 1.352564, mix_ce: 0.253550
[11:46:30.285] iteration 508: loss: 0.783875, mix_dice: 1.464279, mix_ce: 0.103472
[11:46:30.473] iteration 509: loss: 0.726254, mix_dice: 1.207826, mix_ce: 0.244682
[11:46:30.743] iteration 510: loss: 0.723303, mix_dice: 1.245972, mix_ce: 0.200634
[11:46:31.171] iteration 511: loss: 0.849392, mix_dice: 1.408114, mix_ce: 0.290670
[11:46:31.439] iteration 512: loss: 0.787713, mix_dice: 1.387605, mix_ce: 0.187821
[11:46:31.583] iteration 513: loss: 0.756425, mix_dice: 1.270915, mix_ce: 0.241935
[11:46:31.668] iteration 514: loss: 0.830751, mix_dice: 1.499227, mix_ce: 0.162275
[11:46:31.834] iteration 515: loss: 0.765794, mix_dice: 1.324442, mix_ce: 0.207146
[11:46:32.020] iteration 516: loss: 0.736143, mix_dice: 1.287403, mix_ce: 0.184883
[11:46:32.165] iteration 517: loss: 0.778423, mix_dice: 1.365164, mix_ce: 0.191683
[11:46:32.313] iteration 518: loss: 0.791601, mix_dice: 1.429238, mix_ce: 0.153964
[11:46:32.466] iteration 519: loss: 0.802407, mix_dice: 1.314187, mix_ce: 0.290628
[11:46:32.622] iteration 520: loss: 0.784673, mix_dice: 1.343677, mix_ce: 0.225670
[11:46:32.746] iteration 521: loss: 0.775562, mix_dice: 1.357099, mix_ce: 0.194025
[11:46:32.810] iteration 522: loss: 0.738012, mix_dice: 1.320255, mix_ce: 0.155770
[11:46:32.867] iteration 523: loss: 0.838343, mix_dice: 1.452083, mix_ce: 0.224602
[11:46:32.935] iteration 524: loss: 0.829308, mix_dice: 1.462002, mix_ce: 0.196615
[11:46:33.057] iteration 525: loss: 0.794063, mix_dice: 1.386415, mix_ce: 0.201711
[11:46:34.436] iteration 526: loss: 0.757350, mix_dice: 1.329884, mix_ce: 0.184817
[11:46:34.485] iteration 527: loss: 0.887715, mix_dice: 1.571497, mix_ce: 0.203932
[11:46:34.537] iteration 528: loss: 0.832995, mix_dice: 1.468171, mix_ce: 0.197818
[11:46:34.601] iteration 529: loss: 0.798439, mix_dice: 1.416933, mix_ce: 0.179945
[11:46:34.730] iteration 530: loss: 0.790338, mix_dice: 1.370048, mix_ce: 0.210628
[11:46:34.803] iteration 531: loss: 0.919165, mix_dice: 1.527782, mix_ce: 0.310548
[11:46:34.869] iteration 532: loss: 0.741826, mix_dice: 1.380002, mix_ce: 0.103650
[11:46:34.981] iteration 533: loss: 0.761257, mix_dice: 1.369505, mix_ce: 0.153010
[11:46:35.605] iteration 534: loss: 0.733645, mix_dice: 1.267302, mix_ce: 0.199989
[11:46:35.742] iteration 535: loss: 0.819392, mix_dice: 1.482147, mix_ce: 0.156637
[11:46:35.859] iteration 536: loss: 0.823395, mix_dice: 1.418092, mix_ce: 0.228698
[11:46:35.919] iteration 537: loss: 0.813483, mix_dice: 1.355973, mix_ce: 0.270992
[11:46:36.184] iteration 538: loss: 0.815780, mix_dice: 1.354039, mix_ce: 0.277521
[11:46:36.281] iteration 539: loss: 0.782089, mix_dice: 1.331735, mix_ce: 0.232443
[11:46:36.386] iteration 540: loss: 0.749331, mix_dice: 1.264209, mix_ce: 0.234452
[11:46:36.485] iteration 541: loss: 0.769308, mix_dice: 1.259583, mix_ce: 0.279033
[11:46:36.865] iteration 542: loss: 0.872528, mix_dice: 1.538361, mix_ce: 0.206695
[11:46:36.974] iteration 543: loss: 0.749094, mix_dice: 1.281731, mix_ce: 0.216457
[11:46:37.080] iteration 544: loss: 0.743728, mix_dice: 1.301296, mix_ce: 0.186160
[11:46:37.194] iteration 545: loss: 0.843945, mix_dice: 1.462608, mix_ce: 0.225281
[11:46:37.475] iteration 546: loss: 0.791675, mix_dice: 1.371248, mix_ce: 0.212103
[11:46:38.428] iteration 547: loss: 0.745297, mix_dice: 1.387788, mix_ce: 0.102807
[11:46:38.502] iteration 548: loss: 0.737950, mix_dice: 1.272715, mix_ce: 0.203184
[11:46:38.580] iteration 549: loss: 0.783623, mix_dice: 1.387337, mix_ce: 0.179908
[11:46:38.658] iteration 550: loss: 0.732104, mix_dice: 1.285754, mix_ce: 0.178455
[11:46:38.941] iteration 551: loss: 0.823404, mix_dice: 1.387486, mix_ce: 0.259323
[11:46:39.096] iteration 552: loss: 0.751166, mix_dice: 1.253154, mix_ce: 0.249179
[11:46:39.206] iteration 553: loss: 0.760960, mix_dice: 1.341764, mix_ce: 0.180157
[11:46:39.273] iteration 554: loss: 0.716943, mix_dice: 1.213603, mix_ce: 0.220284
[11:46:39.705] iteration 555: loss: 0.717115, mix_dice: 1.211374, mix_ce: 0.222857
[11:46:39.812] iteration 556: loss: 0.717156, mix_dice: 1.291584, mix_ce: 0.142727
[11:46:39.921] iteration 557: loss: 0.705338, mix_dice: 1.146082, mix_ce: 0.264595
[11:46:40.029] iteration 558: loss: 0.871098, mix_dice: 1.532101, mix_ce: 0.210094
[11:46:40.381] iteration 559: loss: 0.829435, mix_dice: 1.431383, mix_ce: 0.227487
[11:46:40.499] iteration 560: loss: 0.747462, mix_dice: 1.313608, mix_ce: 0.181315
[11:46:40.605] iteration 561: loss: 0.806496, mix_dice: 1.452962, mix_ce: 0.160029
[11:46:40.674] iteration 562: loss: 0.749000, mix_dice: 1.281858, mix_ce: 0.216143
[11:46:40.969] iteration 563: loss: 0.820612, mix_dice: 1.478926, mix_ce: 0.162298
[11:46:41.051] iteration 564: loss: 0.687253, mix_dice: 1.212124, mix_ce: 0.162382
[11:46:41.135] iteration 565: loss: 0.843303, mix_dice: 1.443545, mix_ce: 0.243061
[11:46:41.296] iteration 566: loss: 0.775573, mix_dice: 1.410158, mix_ce: 0.140988
[11:46:41.658] iteration 567: loss: 0.760346, mix_dice: 1.318136, mix_ce: 0.202555
[11:46:42.853] iteration 568: loss: 0.714774, mix_dice: 1.231752, mix_ce: 0.197797
[11:46:42.923] iteration 569: loss: 0.809010, mix_dice: 1.347827, mix_ce: 0.270193
[11:46:43.014] iteration 570: loss: 0.738153, mix_dice: 1.365992, mix_ce: 0.110313
[11:46:43.168] iteration 571: loss: 0.758547, mix_dice: 1.319432, mix_ce: 0.197663
[11:46:43.483] iteration 572: loss: 0.760682, mix_dice: 1.431558, mix_ce: 0.089807
[11:46:43.629] iteration 573: loss: 0.787839, mix_dice: 1.407661, mix_ce: 0.168017
[11:46:43.712] iteration 574: loss: 0.796552, mix_dice: 1.340318, mix_ce: 0.252787
[11:46:43.764] iteration 575: loss: 0.765899, mix_dice: 1.317513, mix_ce: 0.214285
[11:46:44.541] iteration 576: loss: 0.720443, mix_dice: 1.268931, mix_ce: 0.171954
[11:46:44.839] iteration 577: loss: 0.768205, mix_dice: 1.419810, mix_ce: 0.116599
[11:46:44.917] iteration 578: loss: 0.756590, mix_dice: 1.374362, mix_ce: 0.138818
[11:46:44.975] iteration 579: loss: 0.772852, mix_dice: 1.366958, mix_ce: 0.178747
[11:46:45.026] iteration 580: loss: 0.758543, mix_dice: 1.240245, mix_ce: 0.276842
[11:46:45.073] iteration 581: loss: 0.723006, mix_dice: 1.283921, mix_ce: 0.162092
[11:46:45.120] iteration 582: loss: 0.830581, mix_dice: 1.334839, mix_ce: 0.326324
[11:46:45.170] iteration 583: loss: 0.792534, mix_dice: 1.478013, mix_ce: 0.107056
[11:46:45.614] iteration 584: loss: 0.671547, mix_dice: 1.241407, mix_ce: 0.101687
[11:46:45.681] iteration 585: loss: 0.733867, mix_dice: 1.226736, mix_ce: 0.240998
[11:46:45.925] iteration 586: loss: 0.730919, mix_dice: 1.328173, mix_ce: 0.133665
[11:46:46.135] iteration 587: loss: 0.752203, mix_dice: 1.232220, mix_ce: 0.272186
[11:46:46.346] iteration 588: loss: 0.847650, mix_dice: 1.465452, mix_ce: 0.229848
[11:46:47.696] iteration 589: loss: 0.657896, mix_dice: 1.179700, mix_ce: 0.136092
[11:46:47.788] iteration 590: loss: 0.840123, mix_dice: 1.537203, mix_ce: 0.143044
[11:46:47.881] iteration 591: loss: 0.762617, mix_dice: 1.351451, mix_ce: 0.173784
[11:46:47.940] iteration 592: loss: 0.756443, mix_dice: 1.281199, mix_ce: 0.231688
[11:46:48.264] iteration 593: loss: 0.754481, mix_dice: 1.355944, mix_ce: 0.153019
[11:46:48.329] iteration 594: loss: 0.805989, mix_dice: 1.443684, mix_ce: 0.168294
[11:46:48.403] iteration 595: loss: 0.801557, mix_dice: 1.304172, mix_ce: 0.298943
[11:46:48.489] iteration 596: loss: 0.789117, mix_dice: 1.326995, mix_ce: 0.251239
[11:46:49.071] iteration 597: loss: 0.743611, mix_dice: 1.341866, mix_ce: 0.145356
[11:46:49.213] iteration 598: loss: 0.733731, mix_dice: 1.298585, mix_ce: 0.168877
[11:46:49.343] iteration 599: loss: 0.699411, mix_dice: 1.217061, mix_ce: 0.181761
[11:46:49.485] iteration 600: loss: 0.810122, mix_dice: 1.268443, mix_ce: 0.351801
[11:46:49.799] iteration 601: loss: 0.774348, mix_dice: 1.362119, mix_ce: 0.186577
[11:46:49.899] iteration 602: loss: 0.760516, mix_dice: 1.373279, mix_ce: 0.147752
[11:46:49.994] iteration 603: loss: 0.764047, mix_dice: 1.385596, mix_ce: 0.142499
[11:46:50.085] iteration 604: loss: 0.726567, mix_dice: 1.291468, mix_ce: 0.161666
[11:46:50.321] iteration 605: loss: 0.820017, mix_dice: 1.387200, mix_ce: 0.252835
[11:46:50.380] iteration 606: loss: 0.772416, mix_dice: 1.327762, mix_ce: 0.217071
[11:46:50.456] iteration 607: loss: 0.904523, mix_dice: 1.529400, mix_ce: 0.279646
[11:46:50.533] iteration 608: loss: 0.720262, mix_dice: 1.257778, mix_ce: 0.182747
[11:46:50.951] iteration 609: loss: 0.806265, mix_dice: 1.394561, mix_ce: 0.217968
[11:46:51.956] iteration 610: loss: 0.657869, mix_dice: 1.176400, mix_ce: 0.139338
[11:46:52.026] iteration 611: loss: 0.810091, mix_dice: 1.361324, mix_ce: 0.258857
[11:46:52.087] iteration 612: loss: 0.783350, mix_dice: 1.385781, mix_ce: 0.180919
[11:46:52.150] iteration 613: loss: 0.674397, mix_dice: 1.163333, mix_ce: 0.185460
[11:46:52.556] iteration 614: loss: 0.848112, mix_dice: 1.414834, mix_ce: 0.281390
[11:46:52.661] iteration 615: loss: 0.688194, mix_dice: 1.205248, mix_ce: 0.171139
[11:46:52.749] iteration 616: loss: 0.719277, mix_dice: 1.224861, mix_ce: 0.213694
[11:46:52.849] iteration 617: loss: 0.693796, mix_dice: 1.215891, mix_ce: 0.171700
[11:46:53.306] iteration 618: loss: 0.661544, mix_dice: 1.179355, mix_ce: 0.143733
[11:46:53.421] iteration 619: loss: 0.740700, mix_dice: 1.291533, mix_ce: 0.189867
[11:46:53.527] iteration 620: loss: 0.805305, mix_dice: 1.411449, mix_ce: 0.199162
[11:46:53.619] iteration 621: loss: 0.753718, mix_dice: 1.311257, mix_ce: 0.196179
[11:46:54.022] iteration 622: loss: 0.666728, mix_dice: 1.152324, mix_ce: 0.181133
[11:46:54.171] iteration 623: loss: 0.841889, mix_dice: 1.489328, mix_ce: 0.194450
[11:46:54.290] iteration 624: loss: 0.682826, mix_dice: 1.219638, mix_ce: 0.146015
[11:46:54.453] iteration 625: loss: 0.727095, mix_dice: 1.285721, mix_ce: 0.168470
[11:46:54.553] iteration 626: loss: 0.735267, mix_dice: 1.327885, mix_ce: 0.142650
[11:46:54.606] iteration 627: loss: 0.686137, mix_dice: 1.147971, mix_ce: 0.224302
[11:46:54.691] iteration 628: loss: 0.701602, mix_dice: 1.252304, mix_ce: 0.150900
[11:46:54.787] iteration 629: loss: 0.748901, mix_dice: 1.312575, mix_ce: 0.185228
[11:46:55.274] iteration 630: loss: 0.822416, mix_dice: 1.418644, mix_ce: 0.226188
[11:46:56.536] iteration 631: loss: 0.799637, mix_dice: 1.377430, mix_ce: 0.221843
[11:46:56.639] iteration 632: loss: 0.752476, mix_dice: 1.274136, mix_ce: 0.230815
[11:46:56.730] iteration 633: loss: 0.705676, mix_dice: 1.243474, mix_ce: 0.167878
[11:46:56.789] iteration 634: loss: 0.702662, mix_dice: 1.162453, mix_ce: 0.242872
[11:46:57.043] iteration 635: loss: 0.836819, mix_dice: 1.509179, mix_ce: 0.164459
[11:46:57.131] iteration 636: loss: 0.796954, mix_dice: 1.373358, mix_ce: 0.220550
[11:46:57.240] iteration 637: loss: 0.680847, mix_dice: 1.189101, mix_ce: 0.172593
[11:46:57.364] iteration 638: loss: 0.758299, mix_dice: 1.346843, mix_ce: 0.169755
[11:46:57.793] iteration 639: loss: 0.614489, mix_dice: 1.036191, mix_ce: 0.192787
[11:46:57.928] iteration 640: loss: 0.857100, mix_dice: 1.543775, mix_ce: 0.170424
[11:46:58.067] iteration 641: loss: 0.678458, mix_dice: 1.217139, mix_ce: 0.139777
[11:46:58.150] iteration 642: loss: 0.691911, mix_dice: 1.221879, mix_ce: 0.161943
[11:46:58.282] iteration 643: loss: 0.744387, mix_dice: 1.342204, mix_ce: 0.146571
[11:46:58.338] iteration 644: loss: 0.723702, mix_dice: 1.280216, mix_ce: 0.167188
[11:46:58.391] iteration 645: loss: 0.720120, mix_dice: 1.254459, mix_ce: 0.185781
[11:46:58.444] iteration 646: loss: 0.743643, mix_dice: 1.355319, mix_ce: 0.131968
[11:46:59.087] iteration 647: loss: 0.829544, mix_dice: 1.467779, mix_ce: 0.191309
[11:46:59.167] iteration 648: loss: 0.679719, mix_dice: 1.174268, mix_ce: 0.185170
[11:46:59.282] iteration 649: loss: 0.709248, mix_dice: 1.239587, mix_ce: 0.178909
[11:46:59.369] iteration 650: loss: 0.714955, mix_dice: 1.269216, mix_ce: 0.160694
[11:46:59.651] iteration 651: loss: 0.701078, mix_dice: 1.248726, mix_ce: 0.153430
[11:47:00.585] iteration 652: loss: 0.739284, mix_dice: 1.331369, mix_ce: 0.147199
[11:47:00.655] iteration 653: loss: 0.742999, mix_dice: 1.293091, mix_ce: 0.192908
[11:47:00.726] iteration 654: loss: 0.740171, mix_dice: 1.339271, mix_ce: 0.141071
[11:47:00.789] iteration 655: loss: 0.785367, mix_dice: 1.397339, mix_ce: 0.173396
[11:47:01.131] iteration 656: loss: 0.777603, mix_dice: 1.308000, mix_ce: 0.247206
[11:47:01.360] iteration 657: loss: 0.785497, mix_dice: 1.407172, mix_ce: 0.163823
[11:47:01.550] iteration 658: loss: 0.909244, mix_dice: 1.516212, mix_ce: 0.302276
[11:47:01.722] iteration 659: loss: 0.734760, mix_dice: 1.269346, mix_ce: 0.200174
[11:47:01.873] iteration 660: loss: 0.815318, mix_dice: 1.375285, mix_ce: 0.255351
[11:47:02.002] iteration 661: loss: 0.703051, mix_dice: 1.205514, mix_ce: 0.200587
[11:47:02.082] iteration 662: loss: 0.726020, mix_dice: 1.267300, mix_ce: 0.184741
[11:47:02.157] iteration 663: loss: 0.790081, mix_dice: 1.370174, mix_ce: 0.209987
[11:47:02.310] iteration 664: loss: 0.720967, mix_dice: 1.254646, mix_ce: 0.187288
[11:47:02.444] iteration 665: loss: 0.696918, mix_dice: 1.222735, mix_ce: 0.171102
[11:47:02.541] iteration 666: loss: 0.768691, mix_dice: 1.392605, mix_ce: 0.144776
[11:47:02.762] iteration 667: loss: 0.705771, mix_dice: 1.293724, mix_ce: 0.117817
[11:47:02.915] iteration 668: loss: 0.737754, mix_dice: 1.356040, mix_ce: 0.119468
[11:47:03.140] iteration 669: loss: 0.658774, mix_dice: 1.210320, mix_ce: 0.107228
[11:47:03.254] iteration 670: loss: 0.678565, mix_dice: 1.231540, mix_ce: 0.125590
[11:47:03.368] iteration 671: loss: 0.766553, mix_dice: 1.236134, mix_ce: 0.296972
[11:47:03.540] iteration 672: loss: 0.769432, mix_dice: 1.375999, mix_ce: 0.162864
[11:47:04.683] iteration 673: loss: 0.872386, mix_dice: 1.467785, mix_ce: 0.276987
[11:47:04.876] iteration 674: loss: 0.675748, mix_dice: 1.233967, mix_ce: 0.117529
[11:47:04.944] iteration 675: loss: 0.823028, mix_dice: 1.453876, mix_ce: 0.192180
[11:47:05.017] iteration 676: loss: 0.784359, mix_dice: 1.329390, mix_ce: 0.239327
[11:47:05.123] iteration 677: loss: 0.692616, mix_dice: 1.214372, mix_ce: 0.170860
[11:47:05.245] iteration 678: loss: 0.837007, mix_dice: 1.432071, mix_ce: 0.241943
[11:47:05.396] iteration 679: loss: 0.733894, mix_dice: 1.315906, mix_ce: 0.151883
[11:47:05.481] iteration 680: loss: 0.802146, mix_dice: 1.424400, mix_ce: 0.179893
[11:47:06.097] iteration 681: loss: 0.791698, mix_dice: 1.341106, mix_ce: 0.242290
[11:47:06.356] iteration 682: loss: 0.792200, mix_dice: 1.413208, mix_ce: 0.171192
[11:47:06.529] iteration 683: loss: 0.698508, mix_dice: 1.221414, mix_ce: 0.175602
[11:47:06.607] iteration 684: loss: 0.853570, mix_dice: 1.501000, mix_ce: 0.206140
[11:47:06.665] iteration 685: loss: 0.671549, mix_dice: 1.146937, mix_ce: 0.196161
[11:47:06.885] iteration 686: loss: 0.761677, mix_dice: 1.243154, mix_ce: 0.280200
[11:47:07.111] iteration 687: loss: 0.816934, mix_dice: 1.469339, mix_ce: 0.164529
[11:47:07.455] iteration 688: loss: 0.767768, mix_dice: 1.251537, mix_ce: 0.283999
[11:47:07.647] iteration 689: loss: 0.746235, mix_dice: 1.317481, mix_ce: 0.174989
[11:47:07.796] iteration 690: loss: 0.721482, mix_dice: 1.315525, mix_ce: 0.127438
[11:47:07.873] iteration 691: loss: 0.826831, mix_dice: 1.436671, mix_ce: 0.216990
[11:47:07.924] iteration 692: loss: 0.773451, mix_dice: 1.369422, mix_ce: 0.177480
[11:47:08.111] iteration 693: loss: 0.790537, mix_dice: 1.255724, mix_ce: 0.325349
[11:47:10.090] iteration 694: loss: 0.699039, mix_dice: 1.184420, mix_ce: 0.213659
[11:47:10.239] iteration 695: loss: 0.677628, mix_dice: 1.124527, mix_ce: 0.230730
[11:47:10.696] iteration 696: loss: 0.806082, mix_dice: 1.460484, mix_ce: 0.151680
[11:47:11.116] iteration 697: loss: 0.800102, mix_dice: 1.375067, mix_ce: 0.225136
[11:47:11.169] iteration 698: loss: 0.754708, mix_dice: 1.321525, mix_ce: 0.187892
[11:47:11.220] iteration 699: loss: 0.790673, mix_dice: 1.363865, mix_ce: 0.217480
[11:47:11.271] iteration 700: loss: 0.822675, mix_dice: 1.532781, mix_ce: 0.112569
[11:47:11.546] iteration 701: loss: 0.794987, mix_dice: 1.274538, mix_ce: 0.315436
[11:47:11.607] iteration 702: loss: 0.758688, mix_dice: 1.406150, mix_ce: 0.111227
[11:47:11.700] iteration 703: loss: 0.722140, mix_dice: 1.236507, mix_ce: 0.207774
[11:47:11.806] iteration 704: loss: 0.692189, mix_dice: 1.189924, mix_ce: 0.194454
[11:47:12.412] iteration 705: loss: 0.755943, mix_dice: 1.312460, mix_ce: 0.199427
[11:47:12.812] iteration 706: loss: 0.700249, mix_dice: 1.224089, mix_ce: 0.176408
[11:47:12.892] iteration 707: loss: 0.884212, mix_dice: 1.506651, mix_ce: 0.261774
[11:47:12.946] iteration 708: loss: 0.776910, mix_dice: 1.389096, mix_ce: 0.164725
[11:47:13.306] iteration 709: loss: 0.772575, mix_dice: 1.349887, mix_ce: 0.195262
[11:47:13.485] iteration 710: loss: 0.805180, mix_dice: 1.450297, mix_ce: 0.160063
[11:47:13.538] iteration 711: loss: 0.827012, mix_dice: 1.401641, mix_ce: 0.252384
[11:47:13.638] iteration 712: loss: 0.794240, mix_dice: 1.454597, mix_ce: 0.133883
[11:47:13.726] iteration 713: loss: 0.688677, mix_dice: 1.214712, mix_ce: 0.162643
[11:47:13.779] iteration 714: loss: 0.757893, mix_dice: 1.376507, mix_ce: 0.139278
[11:47:15.041] iteration 715: loss: 0.657784, mix_dice: 1.204051, mix_ce: 0.111517
[11:47:15.137] iteration 716: loss: 0.748048, mix_dice: 1.306243, mix_ce: 0.189852
[11:47:15.202] iteration 717: loss: 0.684006, mix_dice: 1.164056, mix_ce: 0.203955
[11:47:15.264] iteration 718: loss: 0.654172, mix_dice: 1.183651, mix_ce: 0.124693
[11:47:15.709] iteration 719: loss: 0.760935, mix_dice: 1.326417, mix_ce: 0.195453
[11:47:15.923] iteration 720: loss: 0.667319, mix_dice: 1.158709, mix_ce: 0.175929
[11:47:16.116] iteration 721: loss: 0.708434, mix_dice: 1.307716, mix_ce: 0.109152
[11:47:16.299] iteration 722: loss: 0.692025, mix_dice: 1.250007, mix_ce: 0.134044
[11:47:16.368] iteration 723: loss: 0.708501, mix_dice: 1.184652, mix_ce: 0.232350
[11:47:16.435] iteration 724: loss: 0.731104, mix_dice: 1.299404, mix_ce: 0.162805
[11:47:16.543] iteration 725: loss: 0.827221, mix_dice: 1.383914, mix_ce: 0.270529
[11:47:16.628] iteration 726: loss: 0.763589, mix_dice: 1.387630, mix_ce: 0.139548
[11:47:16.935] iteration 727: loss: 0.803927, mix_dice: 1.435190, mix_ce: 0.172664
[11:47:17.169] iteration 728: loss: 0.766147, mix_dice: 1.267574, mix_ce: 0.264720
[11:47:17.258] iteration 729: loss: 0.866860, mix_dice: 1.576095, mix_ce: 0.157625
[11:47:17.324] iteration 730: loss: 0.761848, mix_dice: 1.371298, mix_ce: 0.152398
[11:47:17.584] iteration 731: loss: 0.728338, mix_dice: 1.328943, mix_ce: 0.127733
[11:47:17.840] iteration 732: loss: 0.683633, mix_dice: 1.151108, mix_ce: 0.216158
[11:47:18.342] iteration 733: loss: 0.641119, mix_dice: 1.092763, mix_ce: 0.189475
[11:47:18.704] iteration 734: loss: 0.722775, mix_dice: 1.296333, mix_ce: 0.149216
[11:47:18.791] iteration 735: loss: 0.650547, mix_dice: 1.140533, mix_ce: 0.160561
[11:47:19.991] iteration 736: loss: 0.652282, mix_dice: 1.115560, mix_ce: 0.189005
[11:47:20.178] iteration 737: loss: 0.744629, mix_dice: 1.401572, mix_ce: 0.087686
[11:47:20.609] iteration 738: loss: 0.598571, mix_dice: 1.002594, mix_ce: 0.194547
[11:47:20.890] iteration 739: loss: 0.779535, mix_dice: 1.366346, mix_ce: 0.192724
[11:47:20.975] iteration 740: loss: 0.784375, mix_dice: 1.398408, mix_ce: 0.170343
[11:47:21.156] iteration 741: loss: 0.863954, mix_dice: 1.547085, mix_ce: 0.180823
[11:47:21.577] iteration 742: loss: 0.649042, mix_dice: 1.175581, mix_ce: 0.122504
[11:47:22.136] iteration 743: loss: 0.795505, mix_dice: 1.310862, mix_ce: 0.280148
[11:47:22.331] iteration 744: loss: 0.834075, mix_dice: 1.504716, mix_ce: 0.163435
[11:47:22.414] iteration 745: loss: 0.762789, mix_dice: 1.319880, mix_ce: 0.205698
[11:47:22.763] iteration 746: loss: 0.664332, mix_dice: 1.136487, mix_ce: 0.192177
[11:47:23.185] iteration 747: loss: 0.659475, mix_dice: 1.183283, mix_ce: 0.135667
[11:47:23.271] iteration 748: loss: 0.756141, mix_dice: 1.371024, mix_ce: 0.141258
[11:47:23.513] iteration 749: loss: 0.860852, mix_dice: 1.626374, mix_ce: 0.095330
[11:47:23.910] iteration 750: loss: 0.641132, mix_dice: 1.051411, mix_ce: 0.230853
[11:47:24.077] iteration 751: loss: 0.701350, mix_dice: 1.240012, mix_ce: 0.162688
[11:47:24.153] iteration 752: loss: 0.710234, mix_dice: 1.249739, mix_ce: 0.170730
[11:47:24.322] iteration 753: loss: 0.653096, mix_dice: 1.147055, mix_ce: 0.159137
[11:47:24.699] iteration 754: loss: 0.724719, mix_dice: 1.212607, mix_ce: 0.236832
[11:47:24.795] iteration 755: loss: 0.754949, mix_dice: 1.378786, mix_ce: 0.131112
[11:47:24.857] iteration 756: loss: 0.677834, mix_dice: 1.159893, mix_ce: 0.195775
[11:47:26.161] iteration 757: loss: 0.675441, mix_dice: 1.235975, mix_ce: 0.114908
[11:47:26.499] iteration 758: loss: 0.780169, mix_dice: 1.335952, mix_ce: 0.224387
[11:47:26.762] iteration 759: loss: 0.754757, mix_dice: 1.289078, mix_ce: 0.220436
[11:47:26.853] iteration 760: loss: 0.724236, mix_dice: 1.279272, mix_ce: 0.169201
[11:47:26.927] iteration 761: loss: 0.731802, mix_dice: 1.299372, mix_ce: 0.164232
[11:47:27.154] iteration 762: loss: 0.673160, mix_dice: 1.191390, mix_ce: 0.154929
[11:47:27.376] iteration 763: loss: 0.665771, mix_dice: 1.112307, mix_ce: 0.219236
[11:47:27.524] iteration 764: loss: 0.680306, mix_dice: 1.178087, mix_ce: 0.182526
[11:47:27.580] iteration 765: loss: 0.706722, mix_dice: 1.224492, mix_ce: 0.188951
[11:47:27.635] iteration 766: loss: 0.762352, mix_dice: 1.332918, mix_ce: 0.191786
[11:47:27.794] iteration 767: loss: 0.669362, mix_dice: 1.127972, mix_ce: 0.210751
[11:47:27.976] iteration 768: loss: 0.653988, mix_dice: 1.145415, mix_ce: 0.162561
[11:47:28.169] iteration 769: loss: 0.770330, mix_dice: 1.420111, mix_ce: 0.120548
[11:47:28.295] iteration 770: loss: 0.686995, mix_dice: 1.247175, mix_ce: 0.126814
[11:47:28.427] iteration 771: loss: 0.722604, mix_dice: 1.275197, mix_ce: 0.170011
[11:47:28.480] iteration 772: loss: 0.707796, mix_dice: 1.225823, mix_ce: 0.189769
[11:47:28.530] iteration 773: loss: 0.700073, mix_dice: 1.130619, mix_ce: 0.269527
[11:47:28.612] iteration 774: loss: 0.651551, mix_dice: 1.176747, mix_ce: 0.126355
[11:47:28.771] iteration 775: loss: 0.672690, mix_dice: 1.087427, mix_ce: 0.257953
[11:47:29.090] iteration 776: loss: 0.818390, mix_dice: 1.469354, mix_ce: 0.167426
[11:47:29.267] iteration 777: loss: 0.822787, mix_dice: 1.440032, mix_ce: 0.205542
[11:47:30.451] iteration 778: loss: 0.753067, mix_dice: 1.324024, mix_ce: 0.182110
[11:47:30.552] iteration 779: loss: 0.756484, mix_dice: 1.371932, mix_ce: 0.141036
[11:47:30.608] iteration 780: loss: 0.775779, mix_dice: 1.335012, mix_ce: 0.216546
[11:47:30.665] iteration 781: loss: 0.709420, mix_dice: 1.217924, mix_ce: 0.200916
[11:47:30.949] iteration 782: loss: 0.680682, mix_dice: 1.178385, mix_ce: 0.182980
[11:47:31.006] iteration 783: loss: 0.711835, mix_dice: 1.239494, mix_ce: 0.184177
[11:47:31.058] iteration 784: loss: 0.865249, mix_dice: 1.509303, mix_ce: 0.221195
[11:47:31.108] iteration 785: loss: 0.631436, mix_dice: 1.130007, mix_ce: 0.132866
[11:47:31.830] iteration 786: loss: 0.799621, mix_dice: 1.391158, mix_ce: 0.208083
[11:47:31.891] iteration 787: loss: 0.752628, mix_dice: 1.221005, mix_ce: 0.284251
[11:47:31.954] iteration 788: loss: 0.689744, mix_dice: 1.187393, mix_ce: 0.192095
[11:47:32.041] iteration 789: loss: 0.689225, mix_dice: 1.161779, mix_ce: 0.216670
[11:47:32.358] iteration 790: loss: 0.742392, mix_dice: 1.382263, mix_ce: 0.102522
[11:47:32.665] iteration 791: loss: 0.734066, mix_dice: 1.341369, mix_ce: 0.126764
[11:47:32.885] iteration 792: loss: 0.656270, mix_dice: 1.134062, mix_ce: 0.178478
[11:47:33.162] iteration 793: loss: 0.593295, mix_dice: 1.055752, mix_ce: 0.130838
[11:47:33.353] iteration 794: loss: 0.697166, mix_dice: 1.207553, mix_ce: 0.186778
[11:47:33.429] iteration 795: loss: 0.755441, mix_dice: 1.246776, mix_ce: 0.264107
[11:47:33.479] iteration 796: loss: 0.718692, mix_dice: 1.294880, mix_ce: 0.142504
[11:47:33.528] iteration 797: loss: 0.709212, mix_dice: 1.307748, mix_ce: 0.110677
[11:47:33.577] iteration 798: loss: 0.775901, mix_dice: 1.336523, mix_ce: 0.215279
[11:47:34.663] iteration 799: loss: 0.628215, mix_dice: 1.042120, mix_ce: 0.214310
[11:47:34.731] iteration 800: loss: 0.802816, mix_dice: 1.367786, mix_ce: 0.237846
[11:47:34.827] iteration 801: loss: 0.738799, mix_dice: 1.288245, mix_ce: 0.189353
[11:47:34.964] iteration 802: loss: 0.622248, mix_dice: 1.108465, mix_ce: 0.136032
[11:47:35.231] iteration 803: loss: 0.758976, mix_dice: 1.306780, mix_ce: 0.211172
[11:47:35.399] iteration 804: loss: 0.700652, mix_dice: 1.255980, mix_ce: 0.145325
[11:47:35.544] iteration 805: loss: 0.737337, mix_dice: 1.257205, mix_ce: 0.217469
[11:47:35.711] iteration 806: loss: 0.685651, mix_dice: 1.152171, mix_ce: 0.219130
[11:47:35.934] iteration 807: loss: 0.673703, mix_dice: 1.191078, mix_ce: 0.156327
[11:47:35.996] iteration 808: loss: 0.809535, mix_dice: 1.307944, mix_ce: 0.311127
[11:47:36.088] iteration 809: loss: 0.658138, mix_dice: 1.150631, mix_ce: 0.165644
[11:47:36.223] iteration 810: loss: 0.622679, mix_dice: 0.967385, mix_ce: 0.277973
[11:47:36.641] iteration 811: loss: 0.613821, mix_dice: 1.034893, mix_ce: 0.192749
[11:47:36.779] iteration 812: loss: 0.699813, mix_dice: 1.184740, mix_ce: 0.214886
[11:47:36.897] iteration 813: loss: 0.745118, mix_dice: 1.325152, mix_ce: 0.165085
[11:47:36.953] iteration 814: loss: 0.661980, mix_dice: 1.205505, mix_ce: 0.118455
[11:47:37.073] iteration 815: loss: 0.657359, mix_dice: 1.175761, mix_ce: 0.138957
[11:47:37.158] iteration 816: loss: 0.658358, mix_dice: 1.203211, mix_ce: 0.113505
[11:47:37.319] iteration 817: loss: 0.655814, mix_dice: 1.188156, mix_ce: 0.123473
[11:47:37.415] iteration 818: loss: 0.729584, mix_dice: 1.334474, mix_ce: 0.124694
[11:47:37.712] iteration 819: loss: 0.821196, mix_dice: 1.545594, mix_ce: 0.096798
[11:47:38.874] iteration 820: loss: 0.774676, mix_dice: 1.340781, mix_ce: 0.208570
[11:47:38.944] iteration 821: loss: 0.656218, mix_dice: 1.080369, mix_ce: 0.232066
[11:47:39.019] iteration 822: loss: 0.599584, mix_dice: 1.040592, mix_ce: 0.158576
[11:47:39.114] iteration 823: loss: 0.687401, mix_dice: 1.234228, mix_ce: 0.140573
[11:47:39.542] iteration 824: loss: 0.716352, mix_dice: 1.313748, mix_ce: 0.118957
[11:47:39.688] iteration 825: loss: 0.703611, mix_dice: 1.285489, mix_ce: 0.121733
[11:47:39.826] iteration 826: loss: 0.819672, mix_dice: 1.506783, mix_ce: 0.132562
[11:47:39.995] iteration 827: loss: 0.643977, mix_dice: 1.163416, mix_ce: 0.124538
[11:47:40.138] iteration 828: loss: 0.720888, mix_dice: 1.361580, mix_ce: 0.080197
[11:47:40.230] iteration 829: loss: 0.658858, mix_dice: 1.114863, mix_ce: 0.202853
[11:47:40.389] iteration 830: loss: 0.656428, mix_dice: 1.170001, mix_ce: 0.142855
[11:47:40.488] iteration 831: loss: 0.740354, mix_dice: 1.364646, mix_ce: 0.116061
[11:47:40.936] iteration 832: loss: 0.658859, mix_dice: 1.171343, mix_ce: 0.146376
[11:47:41.128] iteration 833: loss: 0.666407, mix_dice: 1.208044, mix_ce: 0.124771
[11:47:41.309] iteration 834: loss: 0.753854, mix_dice: 1.285612, mix_ce: 0.222096
[11:47:41.464] iteration 835: loss: 0.560551, mix_dice: 0.914861, mix_ce: 0.206241
[11:47:41.645] iteration 836: loss: 0.717510, mix_dice: 1.304049, mix_ce: 0.130971
[11:47:41.841] iteration 837: loss: 0.619639, mix_dice: 1.060139, mix_ce: 0.179139
[11:47:41.965] iteration 838: loss: 0.674516, mix_dice: 1.130026, mix_ce: 0.219006
[11:47:42.024] iteration 839: loss: 0.683481, mix_dice: 1.153715, mix_ce: 0.213247
[11:47:42.268] iteration 840: loss: 0.699521, mix_dice: 1.183603, mix_ce: 0.215439
[11:47:43.581] iteration 841: loss: 0.649168, mix_dice: 1.202869, mix_ce: 0.095468
[11:47:43.729] iteration 842: loss: 0.790860, mix_dice: 1.230641, mix_ce: 0.351079
[11:47:43.857] iteration 843: loss: 0.649539, mix_dice: 1.135146, mix_ce: 0.163932
[11:47:44.023] iteration 844: loss: 0.712621, mix_dice: 1.190135, mix_ce: 0.235107
[11:47:44.169] iteration 845: loss: 0.857918, mix_dice: 1.494113, mix_ce: 0.221722
[11:47:44.231] iteration 846: loss: 0.690349, mix_dice: 1.252725, mix_ce: 0.127972
[11:47:44.289] iteration 847: loss: 0.675435, mix_dice: 1.262413, mix_ce: 0.088457
[11:47:44.343] iteration 848: loss: 0.658410, mix_dice: 1.135462, mix_ce: 0.181359
[11:47:44.924] iteration 849: loss: 0.583255, mix_dice: 1.035683, mix_ce: 0.130827
[11:47:45.026] iteration 850: loss: 0.668797, mix_dice: 1.196724, mix_ce: 0.140870
[11:47:45.141] iteration 851: loss: 0.660224, mix_dice: 1.143791, mix_ce: 0.176658
[11:47:45.243] iteration 852: loss: 0.705227, mix_dice: 1.220642, mix_ce: 0.189811
[11:47:45.560] iteration 853: loss: 0.738274, mix_dice: 1.300767, mix_ce: 0.175782
[11:47:45.690] iteration 854: loss: 0.699662, mix_dice: 1.237071, mix_ce: 0.162253
[11:47:45.801] iteration 855: loss: 0.564643, mix_dice: 0.982856, mix_ce: 0.146431
[11:47:45.879] iteration 856: loss: 0.682384, mix_dice: 1.187081, mix_ce: 0.177687
[11:47:46.163] iteration 857: loss: 0.622364, mix_dice: 1.150941, mix_ce: 0.093787
[11:47:46.226] iteration 858: loss: 0.555757, mix_dice: 0.883696, mix_ce: 0.227819
[11:47:46.307] iteration 859: loss: 0.705747, mix_dice: 1.301395, mix_ce: 0.110099
[11:47:46.376] iteration 860: loss: 0.824262, mix_dice: 1.493415, mix_ce: 0.155109
[11:47:47.026] iteration 861: loss: 0.620826, mix_dice: 1.096050, mix_ce: 0.145602
[11:47:48.290] iteration 862: loss: 0.681323, mix_dice: 1.245425, mix_ce: 0.117220
[11:47:48.360] iteration 863: loss: 0.580724, mix_dice: 0.978354, mix_ce: 0.183094
[11:47:48.437] iteration 864: loss: 0.559898, mix_dice: 0.989129, mix_ce: 0.130668
[11:47:48.533] iteration 865: loss: 0.769340, mix_dice: 1.331989, mix_ce: 0.206691
[11:47:49.049] iteration 866: loss: 0.666063, mix_dice: 1.159844, mix_ce: 0.172282
[11:47:49.271] iteration 867: loss: 0.679124, mix_dice: 1.185941, mix_ce: 0.172307
[11:47:49.635] iteration 868: loss: 0.600377, mix_dice: 0.993231, mix_ce: 0.207523
[11:47:49.899] iteration 869: loss: 0.687744, mix_dice: 1.222172, mix_ce: 0.153315
[11:47:49.972] iteration 870: loss: 0.606479, mix_dice: 1.047496, mix_ce: 0.165463
[11:47:50.036] iteration 871: loss: 0.756105, mix_dice: 1.406955, mix_ce: 0.105254
[11:47:50.119] iteration 872: loss: 0.686433, mix_dice: 1.179372, mix_ce: 0.193495
[11:47:50.266] iteration 873: loss: 0.684150, mix_dice: 1.273832, mix_ce: 0.094468
[11:47:50.545] iteration 874: loss: 0.621670, mix_dice: 1.052990, mix_ce: 0.190350
[11:47:50.776] iteration 875: loss: 0.725300, mix_dice: 1.295511, mix_ce: 0.155089
[11:47:50.940] iteration 876: loss: 0.695094, mix_dice: 1.263467, mix_ce: 0.126721
[11:47:51.131] iteration 877: loss: 0.628743, mix_dice: 1.153054, mix_ce: 0.104432
[11:47:51.488] iteration 878: loss: 0.612645, mix_dice: 1.106525, mix_ce: 0.118765
[11:47:51.709] iteration 879: loss: 0.706927, mix_dice: 1.251325, mix_ce: 0.162529
[11:47:52.283] iteration 880: loss: 0.835496, mix_dice: 1.427930, mix_ce: 0.243061
[11:47:52.617] iteration 881: loss: 0.727377, mix_dice: 1.320805, mix_ce: 0.133948
[11:47:52.680] iteration 882: loss: 0.672986, mix_dice: 1.157645, mix_ce: 0.188327
[11:47:55.512] iteration 883: loss: 0.696846, mix_dice: 1.227880, mix_ce: 0.165812
[11:47:55.708] iteration 884: loss: 0.678154, mix_dice: 1.180905, mix_ce: 0.175404
[11:47:55.768] iteration 885: loss: 0.647874, mix_dice: 1.137069, mix_ce: 0.158679
[11:47:55.860] iteration 886: loss: 0.582784, mix_dice: 1.021144, mix_ce: 0.144423
[11:47:56.139] iteration 887: loss: 0.589366, mix_dice: 1.011276, mix_ce: 0.167456
[11:47:56.202] iteration 888: loss: 0.800794, mix_dice: 1.440130, mix_ce: 0.161457
[11:47:56.781] iteration 889: loss: 0.731195, mix_dice: 1.221904, mix_ce: 0.240486
[11:47:56.902] iteration 890: loss: 0.705520, mix_dice: 1.242132, mix_ce: 0.168908
[11:47:57.310] iteration 891: loss: 0.669623, mix_dice: 1.129616, mix_ce: 0.209630
[11:47:57.535] iteration 892: loss: 0.639216, mix_dice: 1.058709, mix_ce: 0.219723
[11:47:57.591] iteration 893: loss: 0.663200, mix_dice: 1.192774, mix_ce: 0.133626
[11:47:57.646] iteration 894: loss: 0.672809, mix_dice: 1.158938, mix_ce: 0.186680
[11:47:57.786] iteration 895: loss: 0.736016, mix_dice: 1.297741, mix_ce: 0.174290
[11:47:57.922] iteration 896: loss: 0.605825, mix_dice: 1.084009, mix_ce: 0.127642
[11:47:58.147] iteration 897: loss: 0.809373, mix_dice: 1.490063, mix_ce: 0.128683
[11:47:58.346] iteration 898: loss: 0.630493, mix_dice: 1.141531, mix_ce: 0.119455
[11:47:58.622] iteration 899: loss: 0.611473, mix_dice: 1.110680, mix_ce: 0.112266
[11:47:58.877] iteration 900: loss: 0.774311, mix_dice: 1.479743, mix_ce: 0.068880
[11:47:58.969] iteration 901: loss: 0.650327, mix_dice: 1.137420, mix_ce: 0.163234
[11:47:59.537] iteration 902: loss: 0.606825, mix_dice: 1.080350, mix_ce: 0.133300
[11:47:59.915] iteration 903: loss: 0.683261, mix_dice: 1.238043, mix_ce: 0.128479
[11:48:01.410] iteration 904: loss: 0.749734, mix_dice: 1.396827, mix_ce: 0.102640
[11:48:01.801] iteration 905: loss: 0.658020, mix_dice: 1.056508, mix_ce: 0.259533
[11:48:02.163] iteration 906: loss: 0.560526, mix_dice: 0.970356, mix_ce: 0.150696
[11:48:02.401] iteration 907: loss: 0.682992, mix_dice: 1.176100, mix_ce: 0.189883
[11:48:02.459] iteration 908: loss: 0.669509, mix_dice: 1.156405, mix_ce: 0.182613
[11:48:02.520] iteration 909: loss: 0.589038, mix_dice: 1.056327, mix_ce: 0.121749
[11:48:02.668] iteration 910: loss: 0.680415, mix_dice: 1.237782, mix_ce: 0.123047
[11:48:02.930] iteration 911: loss: 0.575899, mix_dice: 1.042970, mix_ce: 0.108828
[11:48:03.298] iteration 912: loss: 0.580512, mix_dice: 1.067655, mix_ce: 0.093368
[11:48:03.513] iteration 913: loss: 0.672524, mix_dice: 1.155424, mix_ce: 0.189624
[11:48:03.581] iteration 914: loss: 0.694483, mix_dice: 1.148877, mix_ce: 0.240089
[11:48:03.647] iteration 915: loss: 0.714528, mix_dice: 1.276817, mix_ce: 0.152238
[11:48:03.722] iteration 916: loss: 0.678103, mix_dice: 1.170882, mix_ce: 0.185325
[11:48:03.864] iteration 917: loss: 0.654767, mix_dice: 1.178168, mix_ce: 0.131367
[11:48:04.128] iteration 918: loss: 0.612144, mix_dice: 1.093144, mix_ce: 0.131145
[11:48:04.428] iteration 919: loss: 0.681487, mix_dice: 1.231611, mix_ce: 0.131362
[11:48:04.568] iteration 920: loss: 0.689161, mix_dice: 1.254414, mix_ce: 0.123908
[11:48:05.272] iteration 921: loss: 0.459236, mix_dice: 0.793076, mix_ce: 0.125396
[11:48:05.807] iteration 922: loss: 0.625418, mix_dice: 1.057702, mix_ce: 0.193134
[11:48:05.870] iteration 923: loss: 0.597716, mix_dice: 1.068406, mix_ce: 0.127025
[11:48:05.923] iteration 924: loss: 0.574198, mix_dice: 0.946406, mix_ce: 0.201990
[11:48:07.542] iteration 925: loss: 0.686019, mix_dice: 1.184967, mix_ce: 0.187071
[11:48:07.604] iteration 926: loss: 0.627823, mix_dice: 1.087586, mix_ce: 0.168059
[11:48:07.670] iteration 927: loss: 0.667740, mix_dice: 1.075798, mix_ce: 0.259681
[11:48:07.727] iteration 928: loss: 0.653065, mix_dice: 1.092113, mix_ce: 0.214018
[11:48:07.784] iteration 929: loss: 0.634726, mix_dice: 1.175093, mix_ce: 0.094358
[11:48:07.855] iteration 930: loss: 0.704633, mix_dice: 1.197078, mix_ce: 0.212188
[11:48:07.913] iteration 931: loss: 0.637287, mix_dice: 1.166690, mix_ce: 0.107884
[11:48:07.976] iteration 932: loss: 0.592725, mix_dice: 1.005959, mix_ce: 0.179491
[11:48:08.771] iteration 933: loss: 0.557491, mix_dice: 0.965519, mix_ce: 0.149462
[11:48:09.119] iteration 934: loss: 0.635977, mix_dice: 1.138000, mix_ce: 0.133954
[11:48:09.480] iteration 935: loss: 0.558405, mix_dice: 0.973129, mix_ce: 0.143682
[11:48:09.814] iteration 936: loss: 0.759117, mix_dice: 1.396426, mix_ce: 0.121808
[11:48:09.935] iteration 937: loss: 0.572953, mix_dice: 1.031450, mix_ce: 0.114456
[11:48:09.988] iteration 938: loss: 0.703496, mix_dice: 1.331433, mix_ce: 0.075559
[11:48:10.048] iteration 939: loss: 0.579455, mix_dice: 1.032521, mix_ce: 0.126389
[11:48:10.148] iteration 940: loss: 0.679932, mix_dice: 1.141900, mix_ce: 0.217963
[11:48:10.668] iteration 941: loss: 0.645949, mix_dice: 1.214344, mix_ce: 0.077555
[11:48:11.114] iteration 942: loss: 0.702267, mix_dice: 1.326222, mix_ce: 0.078312
[11:48:11.411] iteration 943: loss: 0.670671, mix_dice: 1.177861, mix_ce: 0.163481
[11:48:11.479] iteration 944: loss: 0.693077, mix_dice: 1.144158, mix_ce: 0.241995
[11:48:11.549] iteration 945: loss: 0.667133, mix_dice: 1.239958, mix_ce: 0.094308
[11:48:13.184] iteration 946: loss: 0.575832, mix_dice: 0.908983, mix_ce: 0.242680
[11:48:13.289] iteration 947: loss: 0.554526, mix_dice: 0.991245, mix_ce: 0.117807
[11:48:13.345] iteration 948: loss: 0.717568, mix_dice: 1.176521, mix_ce: 0.258616
[11:48:13.400] iteration 949: loss: 0.578442, mix_dice: 1.003816, mix_ce: 0.153069
[11:48:13.453] iteration 950: loss: 0.733538, mix_dice: 1.331416, mix_ce: 0.135661
[11:48:13.508] iteration 951: loss: 0.683394, mix_dice: 1.150553, mix_ce: 0.216235
[11:48:13.601] iteration 952: loss: 0.609681, mix_dice: 1.047951, mix_ce: 0.171411
[11:48:13.857] iteration 953: loss: 0.664091, mix_dice: 1.184298, mix_ce: 0.143884
[11:48:14.142] iteration 954: loss: 0.707675, mix_dice: 1.277937, mix_ce: 0.137412
[11:48:14.374] iteration 955: loss: 0.612457, mix_dice: 1.057936, mix_ce: 0.166978
[11:48:14.666] iteration 956: loss: 0.721259, mix_dice: 1.263144, mix_ce: 0.179374
[11:48:14.918] iteration 957: loss: 0.682723, mix_dice: 1.258617, mix_ce: 0.106829
[11:48:15.013] iteration 958: loss: 0.703136, mix_dice: 1.219565, mix_ce: 0.186706
[11:48:15.096] iteration 959: loss: 0.543579, mix_dice: 0.920935, mix_ce: 0.166223
[11:48:15.223] iteration 960: loss: 0.662773, mix_dice: 1.201996, mix_ce: 0.123549
[11:48:15.364] iteration 961: loss: 0.554415, mix_dice: 0.985645, mix_ce: 0.123185
[11:48:15.491] iteration 962: loss: 0.570030, mix_dice: 1.034040, mix_ce: 0.106021
[11:48:15.639] iteration 963: loss: 0.649407, mix_dice: 1.109091, mix_ce: 0.189723
[11:48:15.799] iteration 964: loss: 0.726077, mix_dice: 1.130967, mix_ce: 0.321188
[11:48:15.971] iteration 965: loss: 0.718282, mix_dice: 1.302545, mix_ce: 0.134018
[11:48:16.108] iteration 966: loss: 0.660612, mix_dice: 1.114201, mix_ce: 0.207022
[11:48:17.317] iteration 967: loss: 0.570603, mix_dice: 1.010156, mix_ce: 0.131049
[11:48:17.417] iteration 968: loss: 0.647320, mix_dice: 1.137980, mix_ce: 0.156660
[11:48:17.486] iteration 969: loss: 0.650989, mix_dice: 1.124489, mix_ce: 0.177489
[11:48:17.551] iteration 970: loss: 0.624847, mix_dice: 1.092487, mix_ce: 0.157207
[11:48:17.817] iteration 971: loss: 0.599199, mix_dice: 1.041528, mix_ce: 0.156871
[11:48:17.879] iteration 972: loss: 0.720973, mix_dice: 1.288385, mix_ce: 0.153561
[11:48:17.940] iteration 973: loss: 0.704731, mix_dice: 1.244717, mix_ce: 0.164745
[11:48:18.001] iteration 974: loss: 0.570084, mix_dice: 1.008054, mix_ce: 0.132114
[11:48:18.548] iteration 975: loss: 0.671821, mix_dice: 1.196475, mix_ce: 0.147166
[11:48:18.651] iteration 976: loss: 0.593893, mix_dice: 1.024550, mix_ce: 0.163235
[11:48:18.806] iteration 977: loss: 0.763556, mix_dice: 1.408515, mix_ce: 0.118597
[11:48:18.977] iteration 978: loss: 0.754838, mix_dice: 1.413337, mix_ce: 0.096339
[11:48:19.222] iteration 979: loss: 0.522529, mix_dice: 0.887167, mix_ce: 0.157892
[11:48:19.384] iteration 980: loss: 0.604248, mix_dice: 1.002790, mix_ce: 0.205707
[11:48:19.550] iteration 981: loss: 0.613403, mix_dice: 1.080940, mix_ce: 0.145867
[11:48:19.643] iteration 982: loss: 0.680299, mix_dice: 1.249630, mix_ce: 0.110968
[11:48:19.834] iteration 983: loss: 0.569251, mix_dice: 1.000158, mix_ce: 0.138345
[11:48:19.996] iteration 984: loss: 0.694227, mix_dice: 1.293936, mix_ce: 0.094519
[11:48:20.204] iteration 985: loss: 0.748399, mix_dice: 1.317527, mix_ce: 0.179270
[11:48:20.355] iteration 986: loss: 0.592022, mix_dice: 0.968844, mix_ce: 0.215200
[11:48:20.508] iteration 987: loss: 0.685461, mix_dice: 1.246312, mix_ce: 0.124610
[11:48:21.933] iteration 988: loss: 0.638170, mix_dice: 1.153116, mix_ce: 0.123223
[11:48:22.174] iteration 989: loss: 0.592365, mix_dice: 1.016821, mix_ce: 0.167908
[11:48:22.482] iteration 990: loss: 0.584492, mix_dice: 1.042543, mix_ce: 0.126440
[11:48:22.552] iteration 991: loss: 0.672843, mix_dice: 1.140569, mix_ce: 0.205117
[11:48:22.606] iteration 992: loss: 0.580375, mix_dice: 0.939674, mix_ce: 0.221075
[11:48:22.660] iteration 993: loss: 0.548053, mix_dice: 0.933541, mix_ce: 0.162565
[11:48:22.711] iteration 994: loss: 0.647058, mix_dice: 1.132149, mix_ce: 0.161967
[11:48:22.898] iteration 995: loss: 0.648119, mix_dice: 1.152640, mix_ce: 0.143597
[11:48:23.607] iteration 996: loss: 0.558336, mix_dice: 1.017632, mix_ce: 0.099039
[11:48:23.862] iteration 997: loss: 0.695318, mix_dice: 1.130970, mix_ce: 0.259666
[11:48:23.914] iteration 998: loss: 0.889341, mix_dice: 1.633735, mix_ce: 0.144947
[11:48:23.970] iteration 999: loss: 0.596187, mix_dice: 1.075102, mix_ce: 0.117272
[11:48:24.051] iteration 1000: loss: 0.510040, mix_dice: 0.886774, mix_ce: 0.133305
[11:48:24.273] iteration 1001: loss: 0.684615, mix_dice: 1.167400, mix_ce: 0.201830
[11:48:24.590] iteration 1002: loss: 0.471287, mix_dice: 0.822763, mix_ce: 0.119810
[11:48:24.986] iteration 1003: loss: 0.716098, mix_dice: 1.279167, mix_ce: 0.153028
[11:48:25.186] iteration 1004: loss: 0.664033, mix_dice: 1.228880, mix_ce: 0.099187
[11:48:25.372] iteration 1005: loss: 0.606708, mix_dice: 1.076983, mix_ce: 0.136432
[11:48:26.267] iteration 1006: loss: 0.693662, mix_dice: 1.296783, mix_ce: 0.090542
[11:48:26.355] iteration 1007: loss: 0.698537, mix_dice: 1.174419, mix_ce: 0.222656
[11:48:26.552] iteration 1008: loss: 0.694791, mix_dice: 1.200986, mix_ce: 0.188595
[11:48:28.852] iteration 1009: loss: 0.577665, mix_dice: 0.963120, mix_ce: 0.192211
[11:48:28.988] iteration 1010: loss: 0.637841, mix_dice: 1.184391, mix_ce: 0.091290
[11:48:29.251] iteration 1011: loss: 0.727168, mix_dice: 1.306811, mix_ce: 0.147525
[11:48:29.465] iteration 1012: loss: 0.576503, mix_dice: 0.927199, mix_ce: 0.225807
[11:48:29.692] iteration 1013: loss: 0.658412, mix_dice: 1.148723, mix_ce: 0.168102
[11:48:29.858] iteration 1014: loss: 0.597049, mix_dice: 1.027602, mix_ce: 0.166496
[11:48:30.026] iteration 1015: loss: 0.631689, mix_dice: 1.130315, mix_ce: 0.133062
[11:48:30.184] iteration 1016: loss: 0.792733, mix_dice: 1.481486, mix_ce: 0.103980
[11:48:30.332] iteration 1017: loss: 0.719351, mix_dice: 1.156006, mix_ce: 0.282696
[11:48:30.482] iteration 1018: loss: 0.744249, mix_dice: 1.405117, mix_ce: 0.083382
[11:48:30.618] iteration 1019: loss: 0.690734, mix_dice: 1.289500, mix_ce: 0.091969
[11:48:30.869] iteration 1020: loss: 0.698962, mix_dice: 1.142211, mix_ce: 0.255713
[11:48:31.138] iteration 1021: loss: 0.731901, mix_dice: 1.333817, mix_ce: 0.129985
[11:48:31.338] iteration 1022: loss: 0.717444, mix_dice: 1.255080, mix_ce: 0.179809
[11:48:31.554] iteration 1023: loss: 0.602019, mix_dice: 1.004859, mix_ce: 0.199178
[11:48:31.743] iteration 1024: loss: 0.674520, mix_dice: 1.165530, mix_ce: 0.183510
[11:48:31.936] iteration 1025: loss: 0.745140, mix_dice: 1.218390, mix_ce: 0.271890
[11:48:32.156] iteration 1026: loss: 0.555814, mix_dice: 0.951918, mix_ce: 0.159709
[11:48:32.372] iteration 1027: loss: 0.720449, mix_dice: 1.337212, mix_ce: 0.103686
[11:48:32.551] iteration 1028: loss: 0.596806, mix_dice: 1.067411, mix_ce: 0.126202
[11:48:32.617] iteration 1029: loss: 0.763891, mix_dice: 1.367906, mix_ce: 0.159875
[11:48:33.774] iteration 1030: loss: 0.586205, mix_dice: 0.987021, mix_ce: 0.185389
[11:48:33.848] iteration 1031: loss: 0.670460, mix_dice: 1.265212, mix_ce: 0.075707
[11:48:33.921] iteration 1032: loss: 0.708950, mix_dice: 1.317893, mix_ce: 0.100008
[11:48:34.007] iteration 1033: loss: 0.726249, mix_dice: 1.211096, mix_ce: 0.241402
[11:48:34.589] iteration 1034: loss: 0.662153, mix_dice: 1.122436, mix_ce: 0.201870
[11:48:34.796] iteration 1035: loss: 0.669540, mix_dice: 1.155141, mix_ce: 0.183940
[11:48:34.952] iteration 1036: loss: 0.646664, mix_dice: 1.159502, mix_ce: 0.133826
[11:48:35.192] iteration 1037: loss: 0.566393, mix_dice: 1.007193, mix_ce: 0.125592
[11:48:35.371] iteration 1038: loss: 0.600607, mix_dice: 1.075589, mix_ce: 0.125625
[11:48:35.563] iteration 1039: loss: 0.524788, mix_dice: 0.910235, mix_ce: 0.139341
[11:48:35.692] iteration 1040: loss: 0.511555, mix_dice: 0.906462, mix_ce: 0.116648
[11:48:35.760] iteration 1041: loss: 0.706702, mix_dice: 1.214470, mix_ce: 0.198934
[11:48:35.830] iteration 1042: loss: 0.752576, mix_dice: 1.376523, mix_ce: 0.128629
[11:48:35.896] iteration 1043: loss: 0.620027, mix_dice: 1.065322, mix_ce: 0.174732
[11:48:35.965] iteration 1044: loss: 0.676159, mix_dice: 1.179651, mix_ce: 0.172667
[11:48:36.034] iteration 1045: loss: 0.683734, mix_dice: 1.199686, mix_ce: 0.167782
[11:48:36.504] iteration 1046: loss: 0.707469, mix_dice: 1.219896, mix_ce: 0.195043
[11:48:36.629] iteration 1047: loss: 0.682346, mix_dice: 1.196674, mix_ce: 0.168018
[11:48:36.857] iteration 1048: loss: 0.690501, mix_dice: 1.112306, mix_ce: 0.268696
[11:48:37.042] iteration 1049: loss: 0.571939, mix_dice: 1.014039, mix_ce: 0.129840
[11:48:37.412] iteration 1050: loss: 0.624404, mix_dice: 1.085345, mix_ce: 0.163463
[11:48:39.077] iteration 1051: loss: 0.613623, mix_dice: 1.039406, mix_ce: 0.187840
[11:48:39.300] iteration 1052: loss: 0.725816, mix_dice: 1.281796, mix_ce: 0.169835
[11:48:39.484] iteration 1053: loss: 0.664830, mix_dice: 1.172771, mix_ce: 0.156890
[11:48:39.706] iteration 1054: loss: 0.705848, mix_dice: 1.255141, mix_ce: 0.156555
[11:48:39.927] iteration 1055: loss: 0.667109, mix_dice: 1.111368, mix_ce: 0.222849
[11:48:40.052] iteration 1056: loss: 0.771596, mix_dice: 1.340000, mix_ce: 0.203191
[11:48:40.128] iteration 1057: loss: 0.637676, mix_dice: 1.088321, mix_ce: 0.187031
[11:48:40.248] iteration 1058: loss: 0.596435, mix_dice: 1.042457, mix_ce: 0.150413
[11:48:40.435] iteration 1059: loss: 0.646708, mix_dice: 1.111422, mix_ce: 0.181994
[11:48:40.806] iteration 1060: loss: 0.702372, mix_dice: 1.248523, mix_ce: 0.156220
[11:48:41.120] iteration 1061: loss: 0.647286, mix_dice: 1.163811, mix_ce: 0.130760
[11:48:41.277] iteration 1062: loss: 0.725986, mix_dice: 1.319296, mix_ce: 0.132676
[11:48:41.362] iteration 1063: loss: 0.629065, mix_dice: 1.121461, mix_ce: 0.136670
[11:48:41.419] iteration 1064: loss: 0.696954, mix_dice: 1.251177, mix_ce: 0.142731
[11:48:42.061] iteration 1065: loss: 0.645950, mix_dice: 1.033720, mix_ce: 0.258180
[11:48:42.211] iteration 1066: loss: 0.545015, mix_dice: 0.959558, mix_ce: 0.130472
[11:48:42.287] iteration 1067: loss: 0.639105, mix_dice: 1.085279, mix_ce: 0.192932
[11:48:42.520] iteration 1068: loss: 0.625453, mix_dice: 1.155212, mix_ce: 0.095694
[11:48:42.799] iteration 1069: loss: 0.626878, mix_dice: 1.097606, mix_ce: 0.156149
[11:48:42.978] iteration 1070: loss: 0.776603, mix_dice: 1.481513, mix_ce: 0.071693
[11:48:43.243] iteration 1071: loss: 0.629738, mix_dice: 1.123193, mix_ce: 0.136283
[11:48:45.028] iteration 1072: loss: 0.694974, mix_dice: 1.236755, mix_ce: 0.153192
[11:48:45.376] iteration 1073: loss: 0.485503, mix_dice: 0.890086, mix_ce: 0.080921
[11:48:45.814] iteration 1074: loss: 0.645473, mix_dice: 1.093919, mix_ce: 0.197027
[11:48:46.038] iteration 1075: loss: 0.698658, mix_dice: 1.185705, mix_ce: 0.211611
[11:48:46.101] iteration 1076: loss: 0.713383, mix_dice: 1.218871, mix_ce: 0.207895
[11:48:46.182] iteration 1077: loss: 0.606987, mix_dice: 1.059072, mix_ce: 0.154902
[11:48:46.282] iteration 1078: loss: 0.769408, mix_dice: 1.388031, mix_ce: 0.150786
[11:48:46.417] iteration 1079: loss: 0.585864, mix_dice: 1.038558, mix_ce: 0.133170
[11:48:46.535] iteration 1080: loss: 0.599620, mix_dice: 0.998967, mix_ce: 0.200274
[11:48:46.632] iteration 1081: loss: 0.526769, mix_dice: 0.943826, mix_ce: 0.109711
[11:48:46.695] iteration 1082: loss: 0.619791, mix_dice: 1.030620, mix_ce: 0.208962
[11:48:46.761] iteration 1083: loss: 0.728443, mix_dice: 1.288170, mix_ce: 0.168716
[11:48:46.822] iteration 1084: loss: 0.748209, mix_dice: 1.374936, mix_ce: 0.121482
[11:48:46.873] iteration 1085: loss: 0.572743, mix_dice: 1.056585, mix_ce: 0.088901
[11:48:47.021] iteration 1086: loss: 0.542162, mix_dice: 0.908468, mix_ce: 0.175856
[11:48:47.161] iteration 1087: loss: 0.652806, mix_dice: 1.198542, mix_ce: 0.107070
[11:48:47.564] iteration 1088: loss: 0.642944, mix_dice: 1.179654, mix_ce: 0.106234
[11:48:47.655] iteration 1089: loss: 0.721909, mix_dice: 1.259037, mix_ce: 0.184780
[11:48:47.766] iteration 1090: loss: 0.638090, mix_dice: 1.120873, mix_ce: 0.155308
[11:48:47.820] iteration 1091: loss: 0.702174, mix_dice: 1.301981, mix_ce: 0.102368
[11:48:48.326] iteration 1092: loss: 0.597080, mix_dice: 1.065493, mix_ce: 0.128667
[11:48:49.435] iteration 1093: loss: 0.623600, mix_dice: 1.049986, mix_ce: 0.197215
[11:48:49.503] iteration 1094: loss: 0.659479, mix_dice: 1.155975, mix_ce: 0.162984
[11:48:49.583] iteration 1095: loss: 0.761133, mix_dice: 1.443379, mix_ce: 0.078886
[11:48:49.678] iteration 1096: loss: 0.614095, mix_dice: 1.032425, mix_ce: 0.195764
[11:48:50.081] iteration 1097: loss: 0.571179, mix_dice: 0.995720, mix_ce: 0.146638
[11:48:50.164] iteration 1098: loss: 0.552371, mix_dice: 0.990840, mix_ce: 0.113903
[11:48:50.223] iteration 1099: loss: 0.792015, mix_dice: 1.417381, mix_ce: 0.166648
[11:48:50.296] iteration 1100: loss: 0.599093, mix_dice: 1.081076, mix_ce: 0.117109
[11:48:50.794] iteration 1101: loss: 0.575557, mix_dice: 1.065918, mix_ce: 0.085196
[11:48:50.886] iteration 1102: loss: 0.738195, mix_dice: 1.384709, mix_ce: 0.091681
[11:48:50.961] iteration 1103: loss: 0.651505, mix_dice: 1.073425, mix_ce: 0.229585
[11:48:51.040] iteration 1104: loss: 0.520032, mix_dice: 0.952473, mix_ce: 0.087591
[11:48:51.458] iteration 1105: loss: 0.392756, mix_dice: 0.707930, mix_ce: 0.077582
[11:48:51.597] iteration 1106: loss: 0.606751, mix_dice: 1.035682, mix_ce: 0.177820
[11:48:51.733] iteration 1107: loss: 0.616001, mix_dice: 1.014466, mix_ce: 0.217536
[11:48:51.847] iteration 1108: loss: 0.684666, mix_dice: 1.238293, mix_ce: 0.131038
[11:48:52.126] iteration 1109: loss: 0.573900, mix_dice: 0.974948, mix_ce: 0.172852
[11:48:52.293] iteration 1110: loss: 0.622398, mix_dice: 1.114646, mix_ce: 0.130149
[11:48:52.501] iteration 1111: loss: 0.762811, mix_dice: 1.335402, mix_ce: 0.190220
[11:48:52.642] iteration 1112: loss: 0.787259, mix_dice: 1.391904, mix_ce: 0.182613
[11:48:52.772] iteration 1113: loss: 0.722368, mix_dice: 1.309680, mix_ce: 0.135055
[11:48:53.917] iteration 1114: loss: 0.550019, mix_dice: 0.991790, mix_ce: 0.108247
[11:48:54.103] iteration 1115: loss: 0.558445, mix_dice: 1.025051, mix_ce: 0.091838
[11:48:54.320] iteration 1116: loss: 0.630510, mix_dice: 1.163827, mix_ce: 0.097193
[11:48:54.472] iteration 1117: loss: 0.647169, mix_dice: 1.229199, mix_ce: 0.065140
[11:48:54.638] iteration 1118: loss: 0.602961, mix_dice: 1.125368, mix_ce: 0.080553
[11:48:54.823] iteration 1119: loss: 0.761441, mix_dice: 1.353071, mix_ce: 0.169810
[11:48:55.043] iteration 1120: loss: 0.599288, mix_dice: 1.040718, mix_ce: 0.157857
[11:48:55.138] iteration 1121: loss: 0.435709, mix_dice: 0.742273, mix_ce: 0.129146
[11:48:55.245] iteration 1122: loss: 0.677932, mix_dice: 1.241986, mix_ce: 0.113879
[11:48:55.345] iteration 1123: loss: 0.700311, mix_dice: 1.248555, mix_ce: 0.152068
[11:48:55.449] iteration 1124: loss: 0.671529, mix_dice: 1.258039, mix_ce: 0.085020
[11:48:55.576] iteration 1125: loss: 0.622980, mix_dice: 0.944504, mix_ce: 0.301455
[11:48:55.887] iteration 1126: loss: 0.602493, mix_dice: 1.061166, mix_ce: 0.143821
[11:48:55.985] iteration 1127: loss: 0.690417, mix_dice: 1.208598, mix_ce: 0.172236
[11:48:56.094] iteration 1128: loss: 0.623269, mix_dice: 1.121329, mix_ce: 0.125210
[11:48:56.220] iteration 1129: loss: 0.659291, mix_dice: 1.233193, mix_ce: 0.085388
[11:48:56.500] iteration 1130: loss: 0.668028, mix_dice: 1.221339, mix_ce: 0.114717
[11:48:56.591] iteration 1131: loss: 0.607361, mix_dice: 1.074892, mix_ce: 0.139830
[11:48:56.661] iteration 1132: loss: 0.599790, mix_dice: 1.029623, mix_ce: 0.169956
[11:48:56.745] iteration 1133: loss: 0.686589, mix_dice: 1.232658, mix_ce: 0.140521
[11:48:56.992] iteration 1134: loss: 0.653689, mix_dice: 1.139355, mix_ce: 0.168023
[11:48:58.519] iteration 1135: loss: 0.547989, mix_dice: 1.010062, mix_ce: 0.085917
[11:48:58.844] iteration 1136: loss: 0.601502, mix_dice: 1.048717, mix_ce: 0.154288
[11:48:59.080] iteration 1137: loss: 0.661477, mix_dice: 1.194917, mix_ce: 0.128037
[11:48:59.379] iteration 1138: loss: 0.654041, mix_dice: 1.097883, mix_ce: 0.210200
[11:48:59.466] iteration 1139: loss: 0.747184, mix_dice: 1.319992, mix_ce: 0.174376
[11:48:59.518] iteration 1140: loss: 0.767941, mix_dice: 1.359335, mix_ce: 0.176547
[11:48:59.594] iteration 1141: loss: 0.570981, mix_dice: 0.989860, mix_ce: 0.152101
[11:48:59.686] iteration 1142: loss: 0.813044, mix_dice: 1.482587, mix_ce: 0.143501
[11:48:59.797] iteration 1143: loss: 0.590213, mix_dice: 1.025737, mix_ce: 0.154690
[11:48:59.912] iteration 1144: loss: 0.456958, mix_dice: 0.776678, mix_ce: 0.137237
[11:49:00.009] iteration 1145: loss: 0.816652, mix_dice: 1.546326, mix_ce: 0.086978
[11:49:00.101] iteration 1146: loss: 0.814860, mix_dice: 1.431451, mix_ce: 0.198268
[11:49:00.365] iteration 1147: loss: 0.687402, mix_dice: 1.246536, mix_ce: 0.128267
[11:49:00.425] iteration 1148: loss: 0.618361, mix_dice: 1.014312, mix_ce: 0.222409
[11:49:00.488] iteration 1149: loss: 0.642963, mix_dice: 1.089348, mix_ce: 0.196578
[11:49:00.542] iteration 1150: loss: 0.746475, mix_dice: 1.372214, mix_ce: 0.120737
[11:49:00.955] iteration 1151: loss: 0.520297, mix_dice: 0.898680, mix_ce: 0.141913
[11:49:01.023] iteration 1152: loss: 0.606359, mix_dice: 1.085902, mix_ce: 0.126816
[11:49:01.092] iteration 1153: loss: 0.738121, mix_dice: 1.179103, mix_ce: 0.297138
[11:49:01.165] iteration 1154: loss: 0.524004, mix_dice: 0.940991, mix_ce: 0.107016
[11:49:01.562] iteration 1155: loss: 0.629293, mix_dice: 1.149353, mix_ce: 0.109233
[11:49:02.575] iteration 1156: loss: 0.715866, mix_dice: 1.311432, mix_ce: 0.120301
[11:49:02.640] iteration 1157: loss: 0.625908, mix_dice: 1.019506, mix_ce: 0.232311
[11:49:02.710] iteration 1158: loss: 0.567349, mix_dice: 0.949319, mix_ce: 0.185379
[11:49:02.817] iteration 1159: loss: 0.664017, mix_dice: 1.201379, mix_ce: 0.126656
[11:49:03.084] iteration 1160: loss: 0.617465, mix_dice: 1.080724, mix_ce: 0.154205
[11:49:03.268] iteration 1161: loss: 0.687345, mix_dice: 1.227304, mix_ce: 0.147386
[11:49:03.327] iteration 1162: loss: 0.702946, mix_dice: 1.313825, mix_ce: 0.092067
[11:49:03.392] iteration 1163: loss: 0.720052, mix_dice: 1.302034, mix_ce: 0.138070
[11:49:03.805] iteration 1164: loss: 0.611162, mix_dice: 1.118317, mix_ce: 0.104007
[11:49:03.865] iteration 1165: loss: 0.637057, mix_dice: 1.127701, mix_ce: 0.146414
[11:49:03.923] iteration 1166: loss: 0.623916, mix_dice: 1.120344, mix_ce: 0.127488
[11:49:03.981] iteration 1167: loss: 0.563938, mix_dice: 1.029067, mix_ce: 0.098810
[11:49:04.411] iteration 1168: loss: 0.604073, mix_dice: 0.981390, mix_ce: 0.226756
[11:49:04.477] iteration 1169: loss: 0.438080, mix_dice: 0.734701, mix_ce: 0.141460
[11:49:04.538] iteration 1170: loss: 0.604417, mix_dice: 1.037316, mix_ce: 0.171518
[11:49:04.604] iteration 1171: loss: 0.674158, mix_dice: 1.230413, mix_ce: 0.117903
[11:49:05.010] iteration 1172: loss: 0.465583, mix_dice: 0.804115, mix_ce: 0.127050
[11:49:05.071] iteration 1173: loss: 0.650822, mix_dice: 1.197403, mix_ce: 0.104241
[11:49:05.196] iteration 1174: loss: 0.717083, mix_dice: 1.238774, mix_ce: 0.195392
[11:49:05.255] iteration 1175: loss: 0.649845, mix_dice: 1.203207, mix_ce: 0.096484
[11:49:05.694] iteration 1176: loss: 0.520501, mix_dice: 0.916655, mix_ce: 0.124347
[11:49:06.716] iteration 1177: loss: 0.631272, mix_dice: 1.111227, mix_ce: 0.151318
[11:49:06.837] iteration 1178: loss: 0.570235, mix_dice: 0.985936, mix_ce: 0.154534
[11:49:06.939] iteration 1179: loss: 0.511905, mix_dice: 0.932827, mix_ce: 0.090982
[11:49:07.053] iteration 1180: loss: 0.668135, mix_dice: 1.225490, mix_ce: 0.110779
[11:49:07.254] iteration 1181: loss: 0.805224, mix_dice: 1.253746, mix_ce: 0.356702
[11:49:07.331] iteration 1182: loss: 0.666847, mix_dice: 1.247750, mix_ce: 0.085944
[11:49:07.407] iteration 1183: loss: 0.664279, mix_dice: 1.068195, mix_ce: 0.260364
[11:49:07.484] iteration 1184: loss: 0.541218, mix_dice: 0.978499, mix_ce: 0.103937
[11:49:08.176] iteration 1185: loss: 0.618370, mix_dice: 1.107362, mix_ce: 0.129377
[11:49:08.368] iteration 1186: loss: 0.499757, mix_dice: 0.862939, mix_ce: 0.136576
[11:49:08.560] iteration 1187: loss: 0.727488, mix_dice: 1.324964, mix_ce: 0.130012
[11:49:08.739] iteration 1188: loss: 0.545078, mix_dice: 0.963754, mix_ce: 0.126403
[11:49:08.883] iteration 1189: loss: 0.739910, mix_dice: 1.316215, mix_ce: 0.163605
[11:49:08.960] iteration 1190: loss: 0.503670, mix_dice: 0.870264, mix_ce: 0.137077
[11:49:09.031] iteration 1191: loss: 0.480384, mix_dice: 0.844354, mix_ce: 0.116414
[11:49:09.113] iteration 1192: loss: 0.601424, mix_dice: 1.067037, mix_ce: 0.135811
[11:49:09.183] iteration 1193: loss: 0.629057, mix_dice: 1.073616, mix_ce: 0.184499
[11:49:09.231] iteration 1194: loss: 0.699833, mix_dice: 1.281407, mix_ce: 0.118259
[11:49:09.281] iteration 1195: loss: 0.485479, mix_dice: 0.872257, mix_ce: 0.098702
[11:49:09.328] iteration 1196: loss: 0.547614, mix_dice: 0.974919, mix_ce: 0.120309
[11:49:09.841] iteration 1197: loss: 0.539884, mix_dice: 0.892393, mix_ce: 0.187375
[11:49:10.786] iteration 1198: loss: 0.531107, mix_dice: 0.919712, mix_ce: 0.142501
[11:49:10.857] iteration 1199: loss: 0.617545, mix_dice: 1.178691, mix_ce: 0.056398
[11:49:11.000] iteration 1200: loss: 0.510928, mix_dice: 0.933006, mix_ce: 0.088849
[11:49:11.298] iteration 1201: loss: 0.521725, mix_dice: 0.919356, mix_ce: 0.124094
[11:49:11.606] iteration 1202: loss: 0.569262, mix_dice: 0.999895, mix_ce: 0.138630
[11:49:11.901] iteration 1203: loss: 0.522709, mix_dice: 0.915234, mix_ce: 0.130184
[11:49:12.123] iteration 1204: loss: 0.609134, mix_dice: 1.131175, mix_ce: 0.087093
[11:49:12.190] iteration 1205: loss: 0.535596, mix_dice: 0.963243, mix_ce: 0.107950
[11:49:12.249] iteration 1206: loss: 0.676211, mix_dice: 1.153707, mix_ce: 0.198715
[11:49:12.357] iteration 1207: loss: 0.667256, mix_dice: 1.088608, mix_ce: 0.245903
[11:49:12.671] iteration 1208: loss: 0.708771, mix_dice: 1.294275, mix_ce: 0.123266
[11:49:12.997] iteration 1209: loss: 0.668835, mix_dice: 1.138565, mix_ce: 0.199106
[11:49:13.283] iteration 1210: loss: 0.560451, mix_dice: 0.984394, mix_ce: 0.136509
[11:49:13.591] iteration 1211: loss: 0.498306, mix_dice: 0.896727, mix_ce: 0.099886
[11:49:13.806] iteration 1212: loss: 0.582480, mix_dice: 1.059671, mix_ce: 0.105289
[11:49:14.182] iteration 1213: loss: 0.642553, mix_dice: 1.111928, mix_ce: 0.173177
[11:49:14.495] iteration 1214: loss: 0.407768, mix_dice: 0.664740, mix_ce: 0.150797
[11:49:14.774] iteration 1215: loss: 0.595250, mix_dice: 1.038337, mix_ce: 0.152164
[11:49:14.865] iteration 1216: loss: 0.663635, mix_dice: 1.159008, mix_ce: 0.168263
[11:49:15.154] iteration 1217: loss: 0.620262, mix_dice: 1.089654, mix_ce: 0.150870
[11:49:15.458] iteration 1218: loss: 0.623838, mix_dice: 1.036834, mix_ce: 0.210842
[11:49:17.132] iteration 1219: loss: 0.565455, mix_dice: 0.963575, mix_ce: 0.167335
[11:49:17.419] iteration 1220: loss: 0.564532, mix_dice: 1.019418, mix_ce: 0.109645
[11:49:17.634] iteration 1221: loss: 0.647453, mix_dice: 1.119101, mix_ce: 0.175805
[11:49:17.687] iteration 1222: loss: 0.747137, mix_dice: 1.329728, mix_ce: 0.164546
[11:49:17.743] iteration 1223: loss: 0.556449, mix_dice: 0.963866, mix_ce: 0.149032
[11:49:17.802] iteration 1224: loss: 0.615879, mix_dice: 1.021591, mix_ce: 0.210167
[11:49:17.890] iteration 1225: loss: 0.613948, mix_dice: 1.122506, mix_ce: 0.105391
[11:49:18.012] iteration 1226: loss: 0.786806, mix_dice: 1.465802, mix_ce: 0.107810
[11:49:18.143] iteration 1227: loss: 0.628776, mix_dice: 1.142291, mix_ce: 0.115261
[11:49:18.200] iteration 1228: loss: 0.526206, mix_dice: 0.973468, mix_ce: 0.078945
[11:49:18.379] iteration 1229: loss: 0.470735, mix_dice: 0.830495, mix_ce: 0.110974
[11:49:18.507] iteration 1230: loss: 0.642554, mix_dice: 1.204907, mix_ce: 0.080202
[11:49:18.940] iteration 1231: loss: 0.475452, mix_dice: 0.756168, mix_ce: 0.194737
[11:49:19.175] iteration 1232: loss: 0.615875, mix_dice: 1.144921, mix_ce: 0.086828
[11:49:19.346] iteration 1233: loss: 0.568977, mix_dice: 1.025246, mix_ce: 0.112709
[11:49:19.507] iteration 1234: loss: 0.636644, mix_dice: 1.107511, mix_ce: 0.165776
[11:49:19.640] iteration 1235: loss: 0.523853, mix_dice: 0.935013, mix_ce: 0.112693
[11:49:19.780] iteration 1236: loss: 0.465120, mix_dice: 0.822701, mix_ce: 0.107538
[11:49:19.932] iteration 1237: loss: 0.604476, mix_dice: 1.082290, mix_ce: 0.126663
[11:49:20.062] iteration 1238: loss: 0.725849, mix_dice: 1.319316, mix_ce: 0.132382
[11:49:20.312] iteration 1239: loss: 0.630278, mix_dice: 1.127007, mix_ce: 0.133549
[11:49:21.560] iteration 1240: loss: 0.605810, mix_dice: 1.138384, mix_ce: 0.073235
[11:49:21.640] iteration 1241: loss: 0.512413, mix_dice: 0.934369, mix_ce: 0.090458
[11:49:21.804] iteration 1242: loss: 0.483412, mix_dice: 0.881832, mix_ce: 0.084991
[11:49:22.002] iteration 1243: loss: 0.633047, mix_dice: 1.164689, mix_ce: 0.101406
[11:49:22.476] iteration 1244: loss: 0.485977, mix_dice: 0.849941, mix_ce: 0.122013
[11:49:22.739] iteration 1245: loss: 0.622208, mix_dice: 1.060403, mix_ce: 0.184012
[11:49:22.897] iteration 1246: loss: 0.787210, mix_dice: 1.453564, mix_ce: 0.120855
[11:49:23.364] iteration 1247: loss: 0.384100, mix_dice: 0.636625, mix_ce: 0.131575
[11:49:23.924] iteration 1248: loss: 0.522877, mix_dice: 0.949385, mix_ce: 0.096369
[11:49:24.364] iteration 1249: loss: 0.612514, mix_dice: 1.006567, mix_ce: 0.218461
[11:49:24.523] iteration 1250: loss: 0.618869, mix_dice: 1.120251, mix_ce: 0.117488
[11:49:24.624] iteration 1251: loss: 0.539014, mix_dice: 0.919729, mix_ce: 0.158298
[11:49:24.696] iteration 1252: loss: 0.602995, mix_dice: 1.075690, mix_ce: 0.130300
[11:49:24.751] iteration 1253: loss: 0.658890, mix_dice: 1.170356, mix_ce: 0.147425
[11:49:24.811] iteration 1254: loss: 0.441993, mix_dice: 0.748775, mix_ce: 0.135211
[11:49:25.162] iteration 1255: loss: 0.593151, mix_dice: 1.094994, mix_ce: 0.091309
[11:49:26.101] iteration 1256: loss: 0.550312, mix_dice: 0.995136, mix_ce: 0.105488
[11:49:26.297] iteration 1257: loss: 0.687880, mix_dice: 1.248066, mix_ce: 0.127694
[11:49:26.365] iteration 1258: loss: 0.671941, mix_dice: 1.209642, mix_ce: 0.134239
[11:49:26.431] iteration 1259: loss: 0.501751, mix_dice: 0.917961, mix_ce: 0.085540
[11:49:26.494] iteration 1260: loss: 0.436445, mix_dice: 0.807879, mix_ce: 0.065011
[11:49:29.286] iteration 1261: loss: 0.729206, mix_dice: 1.250477, mix_ce: 0.207935
[11:49:29.419] iteration 1262: loss: 0.574569, mix_dice: 0.997799, mix_ce: 0.151339
[11:49:30.001] iteration 1263: loss: 0.728164, mix_dice: 1.219048, mix_ce: 0.237279
[11:49:30.233] iteration 1264: loss: 0.755316, mix_dice: 1.338334, mix_ce: 0.172297
[11:49:30.526] iteration 1265: loss: 0.733383, mix_dice: 1.292085, mix_ce: 0.174681
[11:49:30.967] iteration 1266: loss: 0.703169, mix_dice: 1.300120, mix_ce: 0.106218
[11:49:31.077] iteration 1267: loss: 0.499891, mix_dice: 0.863753, mix_ce: 0.136030
[11:49:31.503] iteration 1268: loss: 0.708510, mix_dice: 1.261498, mix_ce: 0.155521
[11:49:31.603] iteration 1269: loss: 0.523386, mix_dice: 0.911217, mix_ce: 0.135555
[11:49:32.093] iteration 1270: loss: 0.709350, mix_dice: 1.318823, mix_ce: 0.099877
[11:49:32.549] iteration 1271: loss: 0.700843, mix_dice: 1.272973, mix_ce: 0.128714
[11:49:33.173] iteration 1272: loss: 0.686571, mix_dice: 1.229379, mix_ce: 0.143762
[11:49:33.237] iteration 1273: loss: 0.822355, mix_dice: 1.344148, mix_ce: 0.300563
[11:49:33.905] iteration 1274: loss: 0.647826, mix_dice: 1.166706, mix_ce: 0.128945
[11:49:34.690] iteration 1275: loss: 0.470709, mix_dice: 0.779870, mix_ce: 0.161549
[11:49:34.755] iteration 1276: loss: 0.647940, mix_dice: 1.174432, mix_ce: 0.121448
[11:49:34.851] iteration 1277: loss: 0.602664, mix_dice: 1.081933, mix_ce: 0.123394
[11:49:35.480] iteration 1278: loss: 0.524834, mix_dice: 0.955693, mix_ce: 0.093974
[11:49:36.122] iteration 1279: loss: 0.612499, mix_dice: 1.001761, mix_ce: 0.223237
[11:49:36.917] iteration 1280: loss: 0.592119, mix_dice: 1.027165, mix_ce: 0.157074
[11:49:37.058] iteration 1281: loss: 0.560069, mix_dice: 0.964943, mix_ce: 0.155196
[11:49:38.422] iteration 1282: loss: 0.618984, mix_dice: 1.076562, mix_ce: 0.161406
[11:49:38.600] iteration 1283: loss: 0.515488, mix_dice: 0.911443, mix_ce: 0.119534
[11:49:38.823] iteration 1284: loss: 0.593237, mix_dice: 1.080533, mix_ce: 0.105940
[11:49:38.920] iteration 1285: loss: 0.603120, mix_dice: 0.986372, mix_ce: 0.219868
[11:49:39.074] iteration 1286: loss: 0.754933, mix_dice: 1.344792, mix_ce: 0.165073
[11:49:39.389] iteration 1287: loss: 0.765586, mix_dice: 1.295727, mix_ce: 0.235445
[11:49:39.498] iteration 1288: loss: 0.605232, mix_dice: 1.005575, mix_ce: 0.204889
[11:49:39.780] iteration 1289: loss: 0.685160, mix_dice: 1.280568, mix_ce: 0.089752
[11:49:39.910] iteration 1290: loss: 0.570451, mix_dice: 1.018447, mix_ce: 0.122454
[11:49:40.021] iteration 1291: loss: 0.615344, mix_dice: 1.101662, mix_ce: 0.129025
[11:49:40.105] iteration 1292: loss: 0.592685, mix_dice: 1.062752, mix_ce: 0.122618
[11:49:40.387] iteration 1293: loss: 0.477180, mix_dice: 0.843707, mix_ce: 0.110653
[11:49:40.550] iteration 1294: loss: 0.566555, mix_dice: 1.007181, mix_ce: 0.125929
[11:49:40.850] iteration 1295: loss: 0.615058, mix_dice: 1.131161, mix_ce: 0.098955
[11:49:40.981] iteration 1296: loss: 0.749069, mix_dice: 1.405568, mix_ce: 0.092569
[11:49:41.220] iteration 1297: loss: 0.786297, mix_dice: 1.400192, mix_ce: 0.172403
[11:49:41.860] iteration 1298: loss: 0.580855, mix_dice: 1.056326, mix_ce: 0.105383
[11:49:41.918] iteration 1299: loss: 0.699560, mix_dice: 1.276370, mix_ce: 0.122751
[11:49:41.973] iteration 1300: loss: 0.703447, mix_dice: 1.211468, mix_ce: 0.195426
[11:49:42.024] iteration 1301: loss: 0.730316, mix_dice: 1.199337, mix_ce: 0.261294
[11:49:42.108] iteration 1302: loss: 0.709580, mix_dice: 1.208871, mix_ce: 0.210289
[11:49:43.592] iteration 1303: loss: 0.712307, mix_dice: 1.331396, mix_ce: 0.093217
[11:49:43.756] iteration 1304: loss: 0.574370, mix_dice: 1.010897, mix_ce: 0.137842
[11:49:43.973] iteration 1305: loss: 0.670560, mix_dice: 1.200082, mix_ce: 0.141037
[11:49:44.120] iteration 1306: loss: 0.559098, mix_dice: 0.884537, mix_ce: 0.233659
[11:49:44.175] iteration 1307: loss: 0.587378, mix_dice: 1.017079, mix_ce: 0.157676
[11:49:44.234] iteration 1308: loss: 0.664380, mix_dice: 1.149256, mix_ce: 0.179505
[11:49:44.294] iteration 1309: loss: 0.542380, mix_dice: 0.898837, mix_ce: 0.185924
[11:49:44.354] iteration 1310: loss: 0.693719, mix_dice: 1.257120, mix_ce: 0.130318
[11:49:44.458] iteration 1311: loss: 0.618423, mix_dice: 1.129279, mix_ce: 0.107568
[11:49:44.522] iteration 1312: loss: 0.646662, mix_dice: 1.147707, mix_ce: 0.145617
[11:49:44.592] iteration 1313: loss: 0.685938, mix_dice: 1.284202, mix_ce: 0.087674
[11:49:44.707] iteration 1314: loss: 0.670981, mix_dice: 1.163209, mix_ce: 0.178752
[11:49:45.086] iteration 1315: loss: 0.548797, mix_dice: 0.950431, mix_ce: 0.147163
[11:49:45.144] iteration 1316: loss: 0.623347, mix_dice: 1.108119, mix_ce: 0.138576
[11:49:45.204] iteration 1317: loss: 0.794455, mix_dice: 1.443273, mix_ce: 0.145638
[11:49:45.340] iteration 1318: loss: 0.727507, mix_dice: 1.304320, mix_ce: 0.150694
[11:49:45.849] iteration 1319: loss: 0.590407, mix_dice: 1.075623, mix_ce: 0.105191
[11:49:45.981] iteration 1320: loss: 0.668986, mix_dice: 0.994808, mix_ce: 0.343165
[11:49:46.098] iteration 1321: loss: 0.759973, mix_dice: 1.400319, mix_ce: 0.119626
[11:49:46.168] iteration 1322: loss: 0.536033, mix_dice: 0.930706, mix_ce: 0.141361
[11:49:46.345] iteration 1323: loss: 0.721919, mix_dice: 1.285888, mix_ce: 0.157949
[11:49:47.396] iteration 1324: loss: 0.779160, mix_dice: 1.488812, mix_ce: 0.069508
[11:49:47.480] iteration 1325: loss: 0.731263, mix_dice: 1.389154, mix_ce: 0.073371
[11:49:47.548] iteration 1326: loss: 0.502285, mix_dice: 0.890355, mix_ce: 0.114214
[11:49:47.631] iteration 1327: loss: 0.692441, mix_dice: 1.247456, mix_ce: 0.137426
[11:49:47.938] iteration 1328: loss: 0.724560, mix_dice: 1.327862, mix_ce: 0.121258
[11:49:48.090] iteration 1329: loss: 0.642699, mix_dice: 1.056444, mix_ce: 0.228954
[11:49:48.166] iteration 1330: loss: 0.506324, mix_dice: 0.874566, mix_ce: 0.138082
[11:49:48.230] iteration 1331: loss: 0.658741, mix_dice: 1.207345, mix_ce: 0.110136
[11:49:48.676] iteration 1332: loss: 0.634577, mix_dice: 1.082157, mix_ce: 0.186996
[11:49:48.783] iteration 1333: loss: 0.556820, mix_dice: 0.918067, mix_ce: 0.195573
[11:49:48.865] iteration 1334: loss: 0.619287, mix_dice: 1.059510, mix_ce: 0.179065
[11:49:48.944] iteration 1335: loss: 0.534585, mix_dice: 0.925982, mix_ce: 0.143188
[11:49:49.324] iteration 1336: loss: 0.697193, mix_dice: 1.319746, mix_ce: 0.074639
[11:49:49.416] iteration 1337: loss: 0.616738, mix_dice: 1.138298, mix_ce: 0.095179
[11:49:49.494] iteration 1338: loss: 0.534486, mix_dice: 0.977826, mix_ce: 0.091146
[11:49:49.597] iteration 1339: loss: 0.588126, mix_dice: 1.020815, mix_ce: 0.155438
[11:49:49.986] iteration 1340: loss: 0.669996, mix_dice: 1.194217, mix_ce: 0.145776
[11:49:50.111] iteration 1341: loss: 0.582914, mix_dice: 1.024599, mix_ce: 0.141229
[11:49:50.239] iteration 1342: loss: 0.493268, mix_dice: 0.861221, mix_ce: 0.125314
[11:49:50.356] iteration 1343: loss: 0.610425, mix_dice: 1.040105, mix_ce: 0.180744
[11:49:50.602] iteration 1344: loss: 0.538749, mix_dice: 0.967474, mix_ce: 0.110024
[11:49:52.110] iteration 1345: loss: 0.531843, mix_dice: 0.938760, mix_ce: 0.124926
[11:49:52.273] iteration 1346: loss: 0.441161, mix_dice: 0.790809, mix_ce: 0.091512
[11:49:52.383] iteration 1347: loss: 0.591822, mix_dice: 1.091496, mix_ce: 0.092148
[11:49:52.606] iteration 1348: loss: 0.731493, mix_dice: 1.291830, mix_ce: 0.171156
[11:49:52.876] iteration 1349: loss: 0.523613, mix_dice: 0.884978, mix_ce: 0.162248
[11:49:53.040] iteration 1350: loss: 0.708619, mix_dice: 1.312774, mix_ce: 0.104464
[11:49:53.212] iteration 1351: loss: 0.542196, mix_dice: 0.968965, mix_ce: 0.115428
[11:49:53.341] iteration 1352: loss: 0.499065, mix_dice: 0.870293, mix_ce: 0.127836
[11:49:53.426] iteration 1353: loss: 0.675561, mix_dice: 1.305556, mix_ce: 0.045565
[11:49:53.482] iteration 1354: loss: 0.711372, mix_dice: 1.344889, mix_ce: 0.077854
[11:49:53.533] iteration 1355: loss: 0.522598, mix_dice: 0.890065, mix_ce: 0.155132
[11:49:53.593] iteration 1356: loss: 0.649411, mix_dice: 1.136937, mix_ce: 0.161884
[11:49:53.785] iteration 1357: loss: 0.606657, mix_dice: 0.998804, mix_ce: 0.214510
[11:49:53.855] iteration 1358: loss: 0.465032, mix_dice: 0.759430, mix_ce: 0.170634
[11:49:53.929] iteration 1359: loss: 0.647501, mix_dice: 1.158276, mix_ce: 0.136726
[11:49:54.015] iteration 1360: loss: 0.521926, mix_dice: 0.886151, mix_ce: 0.157701
[11:49:54.434] iteration 1361: loss: 0.636883, mix_dice: 1.118586, mix_ce: 0.155181
[11:49:54.502] iteration 1362: loss: 0.894343, mix_dice: 1.648342, mix_ce: 0.140345
[11:49:54.566] iteration 1363: loss: 0.579537, mix_dice: 1.006886, mix_ce: 0.152187
[11:49:54.640] iteration 1364: loss: 0.536357, mix_dice: 0.903834, mix_ce: 0.168880
[11:49:54.992] iteration 1365: loss: 0.535369, mix_dice: 0.925792, mix_ce: 0.144945
[11:49:56.030] iteration 1366: loss: 0.580488, mix_dice: 1.032832, mix_ce: 0.128145
[11:49:56.128] iteration 1367: loss: 0.738387, mix_dice: 1.317867, mix_ce: 0.158907
[11:49:56.245] iteration 1368: loss: 0.434987, mix_dice: 0.710395, mix_ce: 0.159579
[11:49:56.355] iteration 1369: loss: 0.626597, mix_dice: 1.143697, mix_ce: 0.109497
[11:49:56.672] iteration 1370: loss: 0.639538, mix_dice: 1.120490, mix_ce: 0.158586
[11:49:56.796] iteration 1371: loss: 0.612608, mix_dice: 1.116468, mix_ce: 0.108747
[11:49:56.873] iteration 1372: loss: 0.521493, mix_dice: 0.846306, mix_ce: 0.196681
[11:49:56.944] iteration 1373: loss: 0.600829, mix_dice: 1.066061, mix_ce: 0.135597
[11:49:57.383] iteration 1374: loss: 0.439565, mix_dice: 0.786861, mix_ce: 0.092269
[11:49:57.521] iteration 1375: loss: 0.664035, mix_dice: 1.142949, mix_ce: 0.185120
[11:49:57.716] iteration 1376: loss: 0.641610, mix_dice: 1.210385, mix_ce: 0.072835
[11:49:57.837] iteration 1377: loss: 0.539730, mix_dice: 0.964344, mix_ce: 0.115116
[11:49:58.064] iteration 1378: loss: 0.791958, mix_dice: 1.428987, mix_ce: 0.154928
[11:49:58.134] iteration 1379: loss: 0.474694, mix_dice: 0.838137, mix_ce: 0.111250
[11:49:58.189] iteration 1380: loss: 0.633971, mix_dice: 1.135604, mix_ce: 0.132339
[11:49:58.240] iteration 1381: loss: 0.576825, mix_dice: 1.024652, mix_ce: 0.128997
[11:49:58.720] iteration 1382: loss: 0.653043, mix_dice: 1.222641, mix_ce: 0.083446
[11:49:58.835] iteration 1383: loss: 0.466959, mix_dice: 0.836693, mix_ce: 0.097225
[11:49:58.924] iteration 1384: loss: 0.496820, mix_dice: 0.837418, mix_ce: 0.156223
[11:49:58.974] iteration 1385: loss: 0.577264, mix_dice: 0.979182, mix_ce: 0.175346
[11:49:59.212] iteration 1386: loss: 0.569755, mix_dice: 1.037388, mix_ce: 0.102122
[11:50:00.167] iteration 1387: loss: 0.732568, mix_dice: 1.263878, mix_ce: 0.201257
[11:50:00.224] iteration 1388: loss: 0.524661, mix_dice: 0.961653, mix_ce: 0.087670
[11:50:00.278] iteration 1389: loss: 0.602133, mix_dice: 1.112382, mix_ce: 0.091884
[11:50:00.332] iteration 1390: loss: 0.682594, mix_dice: 1.252147, mix_ce: 0.113042
[11:50:00.743] iteration 1391: loss: 0.562322, mix_dice: 1.014990, mix_ce: 0.109654
[11:50:00.801] iteration 1392: loss: 0.569120, mix_dice: 0.958656, mix_ce: 0.179585
[11:50:00.862] iteration 1393: loss: 0.698766, mix_dice: 1.246756, mix_ce: 0.150775
[11:50:00.923] iteration 1394: loss: 0.541366, mix_dice: 0.990840, mix_ce: 0.091891
[11:50:01.443] iteration 1395: loss: 0.511917, mix_dice: 0.908411, mix_ce: 0.115423
[11:50:01.512] iteration 1396: loss: 0.742196, mix_dice: 1.364862, mix_ce: 0.119530
[11:50:01.588] iteration 1397: loss: 0.491592, mix_dice: 0.833031, mix_ce: 0.150153
[11:50:01.651] iteration 1398: loss: 0.727179, mix_dice: 1.317058, mix_ce: 0.137300
[11:50:02.150] iteration 1399: loss: 0.557929, mix_dice: 0.993364, mix_ce: 0.122495
[11:50:02.224] iteration 1400: loss: 0.584074, mix_dice: 1.006728, mix_ce: 0.161420
[11:50:02.284] iteration 1401: loss: 0.611151, mix_dice: 1.163917, mix_ce: 0.058385
[11:50:02.339] iteration 1402: loss: 0.705691, mix_dice: 1.243181, mix_ce: 0.168201
[11:50:02.850] iteration 1403: loss: 0.653546, mix_dice: 1.202778, mix_ce: 0.104313
[11:50:02.932] iteration 1404: loss: 0.514620, mix_dice: 0.905426, mix_ce: 0.123815
[11:50:03.007] iteration 1405: loss: 0.419622, mix_dice: 0.684323, mix_ce: 0.154921
[11:50:03.078] iteration 1406: loss: 0.407621, mix_dice: 0.699055, mix_ce: 0.116187
[11:50:03.532] iteration 1407: loss: 0.581032, mix_dice: 1.037787, mix_ce: 0.124278
[11:50:04.516] iteration 1408: loss: 0.563696, mix_dice: 0.992993, mix_ce: 0.134399
[11:50:04.569] iteration 1409: loss: 0.641614, mix_dice: 1.188906, mix_ce: 0.094321
[11:50:04.623] iteration 1410: loss: 0.630639, mix_dice: 1.159332, mix_ce: 0.101946
[11:50:04.679] iteration 1411: loss: 0.522152, mix_dice: 0.920115, mix_ce: 0.124188
[11:50:05.037] iteration 1412: loss: 0.581084, mix_dice: 1.002949, mix_ce: 0.159218
[11:50:05.148] iteration 1413: loss: 0.659877, mix_dice: 1.107403, mix_ce: 0.212352
[11:50:05.217] iteration 1414: loss: 0.638919, mix_dice: 1.166694, mix_ce: 0.111143
[11:50:05.272] iteration 1415: loss: 0.526282, mix_dice: 0.978794, mix_ce: 0.073770
[11:50:05.783] iteration 1416: loss: 0.712250, mix_dice: 1.303604, mix_ce: 0.120896
[11:50:05.837] iteration 1417: loss: 0.507588, mix_dice: 0.856341, mix_ce: 0.158835
[11:50:05.893] iteration 1418: loss: 0.688407, mix_dice: 1.169872, mix_ce: 0.206943
[11:50:05.944] iteration 1419: loss: 0.562874, mix_dice: 1.043424, mix_ce: 0.082323
[11:50:06.706] iteration 1420: loss: 0.580833, mix_dice: 1.025829, mix_ce: 0.135836
[11:50:06.761] iteration 1421: loss: 0.591978, mix_dice: 1.106078, mix_ce: 0.077878
[11:50:06.821] iteration 1422: loss: 0.506694, mix_dice: 0.801500, mix_ce: 0.211888
[11:50:06.875] iteration 1423: loss: 0.605963, mix_dice: 1.076096, mix_ce: 0.135830
[11:50:07.450] iteration 1424: loss: 0.672662, mix_dice: 1.254661, mix_ce: 0.090664
[11:50:07.511] iteration 1425: loss: 0.552117, mix_dice: 0.937777, mix_ce: 0.166456
[11:50:07.564] iteration 1426: loss: 0.569524, mix_dice: 0.964982, mix_ce: 0.174065
[11:50:07.617] iteration 1427: loss: 0.578903, mix_dice: 1.011525, mix_ce: 0.146282
[11:50:08.066] iteration 1428: loss: 0.475022, mix_dice: 0.829035, mix_ce: 0.121009
[11:50:09.263] iteration 1429: loss: 0.668618, mix_dice: 1.281565, mix_ce: 0.055671
[11:50:09.389] iteration 1430: loss: 0.572457, mix_dice: 1.020277, mix_ce: 0.124638
[11:50:09.505] iteration 1431: loss: 0.601441, mix_dice: 1.122296, mix_ce: 0.080585
[11:50:09.644] iteration 1432: loss: 0.547480, mix_dice: 0.915844, mix_ce: 0.179117
[11:50:09.823] iteration 1433: loss: 0.530917, mix_dice: 0.959449, mix_ce: 0.102385
[11:50:09.898] iteration 1434: loss: 0.636565, mix_dice: 1.164521, mix_ce: 0.108609
[11:50:09.957] iteration 1435: loss: 0.715491, mix_dice: 1.332479, mix_ce: 0.098502
[11:50:10.012] iteration 1436: loss: 0.561402, mix_dice: 0.996585, mix_ce: 0.126219
[11:50:10.295] iteration 1437: loss: 0.609108, mix_dice: 1.131729, mix_ce: 0.086487
[11:50:10.349] iteration 1438: loss: 0.656680, mix_dice: 1.220448, mix_ce: 0.092911
[11:50:10.403] iteration 1439: loss: 0.442363, mix_dice: 0.793737, mix_ce: 0.090989
[11:50:10.464] iteration 1440: loss: 0.618314, mix_dice: 1.093698, mix_ce: 0.142931
[11:50:10.961] iteration 1441: loss: 0.371601, mix_dice: 0.648134, mix_ce: 0.095068
[11:50:11.094] iteration 1442: loss: 0.613139, mix_dice: 0.939568, mix_ce: 0.286709
[11:50:11.184] iteration 1443: loss: 0.553728, mix_dice: 1.007192, mix_ce: 0.100265
[11:50:11.244] iteration 1444: loss: 0.614336, mix_dice: 1.110006, mix_ce: 0.118665
[11:50:11.562] iteration 1445: loss: 0.554838, mix_dice: 1.008339, mix_ce: 0.101338
[11:50:11.623] iteration 1446: loss: 0.819137, mix_dice: 1.513211, mix_ce: 0.125061
[11:50:11.682] iteration 1447: loss: 0.456027, mix_dice: 0.817791, mix_ce: 0.094263
[11:50:11.741] iteration 1448: loss: 0.462134, mix_dice: 0.831911, mix_ce: 0.092358
[11:50:12.182] iteration 1449: loss: 0.766481, mix_dice: 1.409439, mix_ce: 0.123523
[11:50:13.170] iteration 1450: loss: 0.608620, mix_dice: 1.087012, mix_ce: 0.130229
[11:50:13.234] iteration 1451: loss: 0.854673, mix_dice: 1.623369, mix_ce: 0.085978
[11:50:13.305] iteration 1452: loss: 0.725475, mix_dice: 1.377654, mix_ce: 0.073297
[11:50:13.396] iteration 1453: loss: 0.484225, mix_dice: 0.879997, mix_ce: 0.088452
[11:50:13.772] iteration 1454: loss: 0.529850, mix_dice: 0.905058, mix_ce: 0.154641
[11:50:13.854] iteration 1455: loss: 0.502181, mix_dice: 0.868355, mix_ce: 0.136007
[11:50:13.925] iteration 1456: loss: 0.595104, mix_dice: 1.038783, mix_ce: 0.151425
[11:50:13.989] iteration 1457: loss: 0.575666, mix_dice: 1.023652, mix_ce: 0.127679
[11:50:14.588] iteration 1458: loss: 0.454081, mix_dice: 0.746861, mix_ce: 0.161301
[11:50:14.676] iteration 1459: loss: 0.677867, mix_dice: 1.230683, mix_ce: 0.125052
[11:50:14.760] iteration 1460: loss: 0.536830, mix_dice: 0.968760, mix_ce: 0.104901
[11:50:14.874] iteration 1461: loss: 0.731406, mix_dice: 1.266399, mix_ce: 0.196413
[11:50:15.174] iteration 1462: loss: 0.476171, mix_dice: 0.780347, mix_ce: 0.171996
[11:50:15.224] iteration 1463: loss: 0.415802, mix_dice: 0.698099, mix_ce: 0.133505
[11:50:15.279] iteration 1464: loss: 0.479697, mix_dice: 0.864401, mix_ce: 0.094994
[11:50:15.336] iteration 1465: loss: 0.592730, mix_dice: 1.076214, mix_ce: 0.109246
[11:50:15.839] iteration 1466: loss: 0.703784, mix_dice: 1.281246, mix_ce: 0.126321
[11:50:15.895] iteration 1467: loss: 0.447264, mix_dice: 0.743448, mix_ce: 0.151080
[11:50:15.954] iteration 1468: loss: 0.551508, mix_dice: 0.961781, mix_ce: 0.141235
[11:50:16.016] iteration 1469: loss: 0.722655, mix_dice: 1.301043, mix_ce: 0.144268
[11:50:16.519] iteration 1470: loss: 0.731097, mix_dice: 1.333586, mix_ce: 0.128608
[11:50:17.572] iteration 1471: loss: 0.542380, mix_dice: 0.933933, mix_ce: 0.150828
[11:50:17.625] iteration 1472: loss: 0.869692, mix_dice: 1.633663, mix_ce: 0.105721
[11:50:17.679] iteration 1473: loss: 0.575903, mix_dice: 0.983057, mix_ce: 0.168749
[11:50:17.736] iteration 1474: loss: 0.647412, mix_dice: 1.189890, mix_ce: 0.104934
[11:50:18.128] iteration 1475: loss: 0.706833, mix_dice: 1.273660, mix_ce: 0.140006
[11:50:18.199] iteration 1476: loss: 0.649012, mix_dice: 1.142277, mix_ce: 0.155748
[11:50:18.289] iteration 1477: loss: 0.703436, mix_dice: 1.240393, mix_ce: 0.166480
[11:50:18.358] iteration 1478: loss: 0.523094, mix_dice: 0.944877, mix_ce: 0.101311
[11:50:18.950] iteration 1479: loss: 0.440933, mix_dice: 0.763958, mix_ce: 0.117909
[11:50:19.020] iteration 1480: loss: 0.545555, mix_dice: 0.956534, mix_ce: 0.134576
[11:50:19.078] iteration 1481: loss: 0.547526, mix_dice: 0.949842, mix_ce: 0.145210
[11:50:19.138] iteration 1482: loss: 0.526953, mix_dice: 0.940701, mix_ce: 0.113205
[11:50:19.506] iteration 1483: loss: 0.677950, mix_dice: 1.154278, mix_ce: 0.201621
[11:50:19.576] iteration 1484: loss: 0.578124, mix_dice: 1.043112, mix_ce: 0.113135
[11:50:19.649] iteration 1485: loss: 0.583130, mix_dice: 1.041079, mix_ce: 0.125182
[11:50:19.727] iteration 1486: loss: 0.587252, mix_dice: 0.961271, mix_ce: 0.213234
[11:50:20.124] iteration 1487: loss: 0.660561, mix_dice: 1.215718, mix_ce: 0.105404
[11:50:20.219] iteration 1488: loss: 0.647695, mix_dice: 1.238094, mix_ce: 0.057297
[11:50:20.335] iteration 1489: loss: 0.683320, mix_dice: 1.203130, mix_ce: 0.163511
[11:50:20.390] iteration 1490: loss: 0.434371, mix_dice: 0.796613, mix_ce: 0.072129
[11:50:21.018] iteration 1491: loss: 0.488154, mix_dice: 0.868377, mix_ce: 0.107931
[11:50:22.163] iteration 1492: loss: 0.623723, mix_dice: 1.121155, mix_ce: 0.126291
[11:50:22.245] iteration 1493: loss: 0.673857, mix_dice: 1.281183, mix_ce: 0.066532
[11:50:22.329] iteration 1494: loss: 0.440693, mix_dice: 0.793636, mix_ce: 0.087750
[11:50:22.406] iteration 1495: loss: 0.466946, mix_dice: 0.834494, mix_ce: 0.099397
[11:50:22.708] iteration 1496: loss: 0.633086, mix_dice: 1.189831, mix_ce: 0.076341
[11:50:22.760] iteration 1497: loss: 0.702943, mix_dice: 1.193021, mix_ce: 0.212864
[11:50:22.890] iteration 1498: loss: 0.620580, mix_dice: 1.063973, mix_ce: 0.177187
[11:50:23.156] iteration 1499: loss: 0.394875, mix_dice: 0.655215, mix_ce: 0.134536
[11:50:23.416] iteration 1500: loss: 0.670713, mix_dice: 1.229761, mix_ce: 0.111666
