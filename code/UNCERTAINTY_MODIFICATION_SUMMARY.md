# 不确定性计算方法修改总结

## 修改概述

将 `slic_uncertainty_cutmix_visualization_synapse.py` 中的不确定性计算方法从 **Monte Carlo Dropout** 改为 **基于模型logits的熵计算**。

## 主要更改

### 1. 新增函数：`compute_logits_uncertainty()`

```python
def compute_logits_uncertainty(model, image):
    """直接从模型logits计算不确定性（基于熵）"""
    model.eval()  # 设置为评估模式
    
    with torch.no_grad():
        logits = model(image)  # 获取原始logits
        probabilities = torch.softmax(logits, dim=1)  # 转换为概率
        entropy_uncertainty = compute_entropy(probabilities)  # 计算熵
    
    return probabilities, entropy_uncertainty
```

### 2. 修改主函数中的不确定性计算调用

**修改前：**
```python
_, uncertainty2 = monte_carlo_dropout(model, image2_tensor, num_samples=15)
```

**修改后：**
```python
_, uncertainty2 = compute_logits_uncertainty(model, image2_tensor)
```

### 3. 更新可视化标题和说明文本

- 图表标题：从 "Entropy Uncertainty" 改为 "Logits-based Entropy Uncertainty"
- 主标题：添加了 "Logits Entropy" 说明
- 统计信息：更新了处理步骤说明，明确指出使用logits-based entropy

## 方法对比

### Monte Carlo Dropout 方法
- **原理**：多次前向传播（启用dropout），计算预测的平均值和方差
- **优点**：能够捕获模型的认知不确定性
- **缺点**：计算成本高，需要多次推理

### Logits-based Entropy 方法
- **原理**：单次前向传播，直接从logits计算softmax概率，然后计算熵
- **优点**：计算效率高，只需一次推理
- **缺点**：主要捕获数据不确定性，不包含模型不确定性

## 技术细节

### 熵计算公式
```
H(p) = -Σ p_i * log(p_i)
```
其中 p_i 是第i个类别的预测概率。

### 实现要点
1. 模型设置为 `eval()` 模式以禁用dropout
2. 使用 `torch.no_grad()` 减少内存使用
3. 通过 `torch.softmax()` 将logits转换为概率分布
4. 调用现有的 `compute_entropy()` 函数计算熵值

## 性能提升

根据测试结果，新方法相比Monte Carlo Dropout：
- **计算速度**：显著提升（无需多次推理）
- **内存使用**：大幅减少（不需要存储多个预测结果）
- **确定性**：结果完全确定（无随机性）

## 保留的功能

- 保留了原始的 `monte_carlo_dropout()` 函数以备比较使用
- 所有其他功能（SLIC分割、区域选择、CutMix操作等）保持不变
- 可视化流程和输出格式保持一致

## 使用建议

新的logits-based方法适用于：
- 需要快速不确定性估计的场景
- 计算资源受限的环境
- 实时或近实时的应用

如果需要更全面的不确定性估计（包括模型不确定性），仍可使用原始的Monte Carlo Dropout方法。
